import numpy as np

import pandas as pd

from utils.point_sampling import (edge_selection, grid_selection, grid_strict_selection,
                                  cc_selection)
from utils.point_tools import sampling_size_count
from utils.point_sorting import sort_coords

import logging

logger = logging.getLogger(__name__)

class SamplingModel:
    def __init__(self, data, sampled_df=None, shot_id_col=None, x_col=None, y_col=None):
        self.data = data
        self.shot_id_col_name = shot_id_col
        self.x_col_name = x_col
        self.y_col_name = y_col
        self.sampled_df = sampled_df
        if sampled_df is not None:
            self.sampled_coords = self.sampled_df[[self.x_col_name, self.y_col_name]].values
        else:
            self.sampled_coords = None
        self.sorted_flag = False
        self.manual_mode = False  # 手动模式标志
        self.has_manual_sampling = False  # 是否进行过手动采样的标志
        self.temp_coords = None  # 添加临时存储排序前的坐标
        self._cancel_requested = False  # 取消标志
        logger.debug("采样模型初始化完成")

    def cancel_operation(self):
        """标记取消请求"""
        logger.debug("采样模型收到取消请求")
        self._cancel_requested = True

    def clean(self):
        self.sampled_coords = None
        self.sampled_df = None
        self.has_manual_sampling = False  # 重置手动采样标志
        self._cancel_requested = False
        logger.info("Sampling Model清除")

    def toggle_point(self, x, y, xlim=None, ylim=None):
        """切换某个坐标点的选择状态
        
        Args:
            x: 点击位置的X坐标
            y: 点击位置的Y坐标
            xlim: 当前视图的X轴范围 (min_x, max_x)
            ylim: 当前视图的Y轴范围 (min_y, max_y)
            
        Returns:
            (bool, bool): 操作是否成功，是否有变化
        """
        if not self.manual_mode:
            return False, False

        # 找到最近的原始数据点
        coords = self.get_coords()
        
        # 如果提供了视图范围，则只考虑视图范围内的点
        if xlim is not None and ylim is not None:
            in_view_mask = (
                (coords[:, 0] >= xlim[0]) & (coords[:, 0] <= xlim[1]) &
                (coords[:, 1] >= ylim[0]) & (coords[:, 1] <= ylim[1])
            )
            
            # 如果视图范围内没有点，则返回
            if not np.any(in_view_mask):
                return False, False
                
            coords = coords[in_view_mask]
        
        distances = np.sqrt(((coords[:, 0] - x) ** 2) + ((coords[:, 1] - y) ** 2))
        nearest_idx = np.argmin(distances)
        nearest_point = coords[nearest_idx]

        # 保存当前状态以便恢复
        old_coords = self.sampled_coords.copy() if self.sampled_coords is not None else None

        # 如果点已被选中则移除,否则添加
        if self.sampled_coords is None:
            self.sampled_coords = np.array([nearest_point])
            self.update_sampled_df()
            self.has_manual_sampling = True  # 设置手动采样标志
            return True, True
        else:
            # 检查是否已选择
            selected_mask = np.all(self.sampled_coords == nearest_point, axis=1)
            if np.any(selected_mask):
                # 移除该点
                self.sampled_coords = self.sampled_coords[~selected_mask]
            else:
                # 添加该点
                self.sampled_coords = np.vstack([self.sampled_coords, nearest_point])

            try:
                self.update_sampled_df()
                self.has_manual_sampling = True  # 设置手动采样标志
                return True, True
            except Exception as e:
                # 如果更新失败，恢复原始状态
                self.sampled_coords = old_coords
                if old_coords is not None:
                    self.update_sampled_df()
                raise e  # 重新抛出异常

    def update_sampled_df(self):
        """更新采样数据框"""
        sampled_coords_df = pd.DataFrame(self.sampled_coords, columns=[self.x_col_name, self.y_col_name])
        self.sampled_df = pd.merge(sampled_coords_df, self.data,
                                  on=[self.x_col_name, self.y_col_name], how='left')
        if self.shot_id_col_name:
            desired_order = ([self.shot_id_col_name, self.x_col_name, self.y_col_name] +
                           [col for col in self.sampled_df.columns
                            if col not in [self.shot_id_col_name, self.x_col_name, self.y_col_name]])
        else:
            desired_order = ([self.x_col_name, self.y_col_name] +
                           [col for col in self.sampled_df.columns
                            if col not in [self.x_col_name, self.y_col_name]])
        self.sampled_df = self.sampled_df[desired_order]

    def sample_points(self, sampling_params, sampling_unit, sampling_method, prefer=None):
        logger.info("更新采样结果")
        self._cancel_requested = False  # 重置取消标志

        def apply_sampling(coords, method, sampling_params, prefer):
            # 检查取消标志
            if self._cancel_requested:
                logger.debug("检测到取消请求，停止采样")
                return None

            unique_x_size = np.unique(coords[:, 0]).size
            unique_y_size = np.unique(coords[:, 1]).size
            
            # 确定采样尺寸
            if sampling_params.get('use_sampling_size'):
                sampling_size = [
                    sampling_params.get('sampling_size_x'),
                    sampling_params.get('sampling_size_y'),
                ]
                if sampling_size[0] is None or sampling_size[1] is None:
                    raise ValueError("采样尺寸不能为空")
            else:
                num_points = sampling_params.get('num_points')
                if num_points is None:
                    raise ValueError("采样点数不能为空")
                    
                if prefer is None:
                    min_x, min_y = np.min(coords, axis=0)
                    max_x, max_y = np.max(coords, axis=0)
                    prefer = 'X' if max_x - min_x >= max_y - min_y else 'Y'
                
                sampling_size = sampling_size_count(unique_x_size, unique_y_size, num_points, method, prefer)
            
            # 检查取消标志
            if self._cancel_requested:
                logger.debug("检测到取消请求，停止采样")
                return None

            sample_result = None
            if method == 'edge':
                sample_result = edge_selection(coords, prefer, sampling_size, self)
            elif method == 'grid':
                sample_result = grid_selection(coords, 'grid', sampling_size, cancel_checker=self)
            elif method == 'cc':
                sample_result = cc_selection(coords, prefer, sampling_size, self)
            elif method == 'grid_strict':
                sample_result = grid_strict_selection(coords, prefer, sampling_size, self)

            if sample_result is None or sample_result.size == 0:
                raise Exception("可接受时间内无法选出指定数量的点")
            else:
                return sample_result

        if sampling_unit == "每Shot" and self.shot_id_col_name:
            sampled_coords = []
            for _, group in self.data.groupby(self.shot_id_col_name):
                # 检查取消标志
                if self._cancel_requested:
                    logger.debug("检测到取消请求，停止采样")
                    return None

                coords = group[[self.x_col_name, self.y_col_name]].values
                sampled = apply_sampling(coords, sampling_method, sampling_params, prefer)
                if sampled is None:  # 如果采样被取消
                    return None
                sampled_coords.extend(sampled)
            self.sampled_coords = np.array(sampled_coords)
        else:
            coords = self.data[[self.x_col_name, self.y_col_name]].values
            self.sampled_coords = apply_sampling(coords, sampling_method, sampling_params, prefer)
            if self.sampled_coords is None:  # 如果采样被取消
                return None
            
        # 最后一次检查取消标志
        if self._cancel_requested:
            logger.debug("检测到取消请求，停止采样")
            return None

        sampled_coords_df = pd.DataFrame(self.sampled_coords, columns=[self.x_col_name, self.y_col_name])
        self.sampled_df = pd.merge(sampled_coords_df, self.data, on=[self.x_col_name, self.y_col_name], how='left')
        if self.shot_id_col_name:
            desired_order = ([self.shot_id_col_name, self.x_col_name, self.y_col_name] +
                             [col for col in self.sampled_df.columns
                              if col not in [self.shot_id_col_name, self.x_col_name, self.y_col_name]])
        else:
            desired_order = ([self.x_col_name, self.y_col_name] +
                             [col for col in self.sampled_df.columns
                              if col not in [self.x_col_name, self.y_col_name]])
        self.sampled_df = self.sampled_df[desired_order]
        return self.sampled_coords

    def sort_points(self, sort_mode_list):
        """排序时先保存原始采样结果"""
        logger.info("更新排序结果")
        self._cancel_requested = False  # 重置取消标志

        if self.sampled_coords is None:
            raise ValueError("待排序坐标为空")

        # 保存排序前的坐标
        self.temp_coords = self.sampled_coords.copy()

        try:
            # 检查取消标志
            if self._cancel_requested:
                logger.debug("检测到取消请求，停止排序")
                return None

            if self.shot_id_col_name:
                shot_id = np.array(self.sampled_df[self.shot_id_col_name])
                sorted_coords = sort_coords(self.sampled_coords, sort_mode_list, shot_id, self)
            else:
                sorted_coords = sort_coords(self.sampled_coords, sort_mode_list, cancel_checker=self)

            # 最后一次检查取消标志
            if self._cancel_requested:
                logger.debug("检测到取消请求，停止排序")
                return None

            self.sampled_coords = sorted_coords
            self.update_sampled_df()
            self.temp_coords = None  # 排序成功后清除临时存储
            return self.sampled_coords
        except Exception as e:
            # 排序失败时恢复原始采样结果
            if self.temp_coords is not None:
                self.sampled_coords = self.temp_coords
                self.update_sampled_df()
                self.temp_coords = None
            raise e  # 继续抛出异常

    def get_coords(self):
        return self.data[[self.x_col_name, self.y_col_name]].values

    def get_plot_data(self):
        return {
            'original_x': self.data[self.x_col_name].values,
            'original_y': self.data[self.y_col_name].values,
            'sampled_x': self.sampled_coords[:, 0] if self.sampled_coords is not None else None,
            'sampled_y': self.sampled_coords[:, 1] if self.sampled_coords is not None else None,
            'sorted_flag': self.sorted_flag and self.sampled_coords is not None  # 确保有采样点时才显示排序
        }

    def check_cancelled(self):
        """检查是否请求取消"""
        return self._cancel_requested
