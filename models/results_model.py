import pickle
import os
from PySide6.QtCore import Qt, QAbstractListModel, QModelIndex, QStandardPaths, QSortFilterProxyModel, QMimeData, QByteArray, QDataStream, QIODevice
from utils.derivative_strategies.derivative_factory import DerivativeStrategyFactory
import logging

logger = logging.getLogger(__name__)


class SheetProxyModel(QSortFilterProxyModel):
    """按Sheet名称过滤的代理模型"""
    def __init__(self, sheet_name, parent=None):
        super().__init__(parent)
        self.sheet_name = sheet_name
        self.setDynamicSortFilter(True)
        
    def filterAcceptsRow(self, source_row, source_parent):
        """过滤指定Sheet的结果"""
        source_model = self.sourceModel()
        index = source_model.index(source_row, 0, source_parent)
        if not index.isValid():
            return False
            
        sheet_name = source_model.get_sheet_name(source_row)
        return sheet_name == self.sheet_name

    def flags(self, index):
        """返回项目标志"""
        if not index.isValid():
            return Qt.NoItemFlags
        
        # 启用拖放功能
        return Qt.ItemIsEnabled | Qt.ItemIsSelectable | Qt.ItemIsDragEnabled | Qt.ItemIsDropEnabled
        
    def canDropMimeData(self, data, action, row, column, parent):
        """检查是否可以接受拖放数据"""
        # 确保我们处理的是列表项数据
        if not data.hasFormat('application/x-qabstractitemmodeldatalist'):
            return False
            
        # 只在同一个模型内部拖放，确保是同一个Sheet
        return True
        
    def dropMimeData(self, data, action, row, column, parent):
        """处理拖放数据"""
        # 将拖放操作转发到源模型
        if not self.sourceModel():
            return False
            
        # 如果是在项目上拖放（而不是在空白区域），转换为行索引
        target_row = parent.row() if parent.isValid() else row
        
        # 获取源索引
        encoded_data = data.data('application/x-qabstractitemmodeldatalist')
        stream = QDataStream(encoded_data, QIODevice.ReadOnly)
        
        # 读取版本信息
        stream.readUInt32()  # 跳过版本
        
        # 读取行数
        count = stream.readUInt32()
        if count == 0:
            return False
            
        # 读取源行（代理模型中的行）
        proxy_source_row = stream.readInt32()
        
        # 计算目标行（如果在末尾拖放，使用行数作为目标）
        if target_row == -1:
            target_row = self.rowCount(QModelIndex())
            
        # 不需要移动的情况
        if proxy_source_row == target_row:
            return False
            
        # 映射到源模型索引
        source_index = self.mapToSource(self.index(proxy_source_row, 0))
        source_row = source_index.row()
        
        # 如果是拖放到末尾，我们需要获取上一个项目的源模型索引
        if target_row == self.rowCount(QModelIndex()):
            if self.rowCount(QModelIndex()) > 0:
                last_proxy_index = self.index(self.rowCount(QModelIndex()) - 1, 0)
                last_source_index = self.mapToSource(last_proxy_index)
                last_row = last_source_index.row()
                
                # 调用源模型的移动方法，将项目移到最后
                return self.sourceModel().move_item_in_sheet(source_row, 1 if last_row < source_row else last_row - source_row + 1)
        else:
            # 映射目标行到源模型索引
            target_index = self.mapToSource(self.index(target_row, 0))
            target_row = target_index.row()
            
            # 计算移动方向
            direction = 1 if target_row > source_row else -1
            
            # 计算移动距离（如果不是相邻）
            distance = abs(target_row - source_row)
            
            # 调用源模型的移动方法
            if distance == 1:
                # 相邻项目，直接移动
                return self.sourceModel().move_item_in_sheet(source_row, direction)
            else:
                # 非相邻项目，需要多次移动
                # 创建一个临时函数进行递归移动
                def move_multiple_steps(current, target, max_steps=100):
                    if max_steps <= 0:
                        return False
                    
                    # 计算当前方向
                    current_direction = 1 if target > current else -1
                    
                    # 移动一步
                    success = self.sourceModel().move_item_in_sheet(current, current_direction)
                    if not success:
                        return False
                    
                    # 检查是否完成
                    new_current = current + current_direction
                    if new_current == target:
                        return True
                    
                    # 递归继续移动
                    return move_multiple_steps(new_current, target, max_steps - 1)
                
                # 执行多步移动
                return move_multiple_steps(source_row, target_row)
        
        return False


class ResultsModel(QAbstractListModel):
    def __init__(self):
        super().__init__()
        self.results = []  # 存储所有采样结果的列表
        self.auto_saved_file = self._get_saved_file_path()
        # 尝试加载临时文件，但忽略结果，因为主控制器会处理是否恢复的逻辑
        self.load_auto_save()
        self.sheet_order = []  # 存储Sheet的显示顺序

    def _get_saved_file_path(self):
        """获取临时文件的完整路径"""
        app_data_path = QStandardPaths.writableLocation(QStandardPaths.AppDataLocation)
        # 确保目录存在
        logger.info(f"临时文件路径: {app_data_path}")
        os.makedirs(app_data_path, exist_ok=True)
        return os.path.join(app_data_path, "auto_save_results.pkl")

    def has_auto_saved_file(self):
        """检查是否存在临时文件"""
        return os.path.exists(self.auto_saved_file)

    def save_progress_to_file(self, filename=None):
        """保存结果到临时文件"""
        try:
            data_to_save = {
                'results': self.results,
                'sheet_order': self.sheet_order
            }
            
            if filename:
                with open(filename, 'wb') as f:
                    pickle.dump(data_to_save, f)
            else:
                with open(self.auto_saved_file, 'wb') as f:
                    pickle.dump(data_to_save, f)
            logger.info(f"临时文件保存成功，完整路径: {self.auto_saved_file}")
        except Exception as e:
            logger.error(f"保存临时文件失败: {str(e)}")

    def _load_data_from_file(self, filename, is_temp_file=False):
        """通用的数据加载方法
        
        Args:
            filename: 要加载的文件路径
            is_temp_file: 是否为临时文件（崩溃恢复文件）
            
        Returns:
            (success, message): 加载是否成功及相关消息
        """
        file_type = "临时" if is_temp_file else "进度"
        
        if not os.path.exists(filename):
            logger.error(f"{file_type}文件不存在: {filename}")
            return False, f"{file_type}文件不存在"
            
        try:
            with open(filename, 'rb') as f:
                loaded_data = pickle.load(f)
            
            # 兼容旧版本的数据格式（直接是结果列表）
            if isinstance(loaded_data, list):
                loaded_results = loaded_data
                loaded_sheet_order = []
            else:
                # 新版本的数据格式（包含结果和sheet顺序）
                loaded_results = loaded_data.get('results', [])
                loaded_sheet_order = loaded_data.get('sheet_order', [])
            
            # 基本类型检查
            if not isinstance(loaded_results, list):
                logger.error(f"{file_type}文件格式不正确：数据不是列表类型 - {filename}")
                return False, f"{file_type}文件格式不正确：数据不是列表类型"
            
            # 尝试访问关键数据结构，验证数据完整性
            # 这将捕获任何缺失或格式不正确的数据
            for i, item in enumerate(loaded_results):
                try:
                    # 验证每个结果的关键属性是否可以访问
                    _ = item['sheet_name']
                    _ = item['project_name']
                    _ = len(item['sampling_result']['sampled_df'])
                    _ = len(item['sampling_result']['original_data'])
                    _ = item['sampling_result']['params']['sort_mode_list']['mode']
                except (KeyError, TypeError, ValueError) as e:
                    logger.error(f"{file_type}文件中第{i+1}项数据结构不完整: {str(e)}")
                    return False, f"{file_type}文件数据结构不完整: {str(e)}"
            
            # 数据验证通过，更新模型
            if not is_temp_file:
                self.beginResetModel()
            
            self.results = loaded_results
            
            # 设置sheet顺序
            if loaded_sheet_order:
                self.sheet_order = loaded_sheet_order
            else:
                # 如果没有保存sheet顺序，根据结果生成
                self.sheet_order = self._generate_sheet_order()
            
            if not is_temp_file:
                self.endResetModel()
                self.save_progress_to_file()  # 同步到临时文件
                
            logger.info(f"{file_type}文件加载成功: {filename}")
            return True, f"{file_type}文件加载成功"
            
        except Exception as e:
            logger.error(f"加载{file_type}文件失败: {str(e)}")
            if is_temp_file:
                self.results = []
                self.sheet_order = []
            return False, f"加载{file_type}文件失败: {str(e)}"
    
    def load_auto_save(self):
        """加载自动保存的崩溃恢复文件"""
        return self._load_data_from_file(self.auto_saved_file, is_temp_file=True)
        
    def load_project(self, filename):
        """加载用户指定的项目文件"""
        return self._load_data_from_file(filename, is_temp_file=False)

    def cleanup_temp_file(self):
        """清理临时文件"""
        if os.path.exists(self.auto_saved_file):
            try:
                os.remove(self.auto_saved_file)
            except Exception as e:
                logger.error(f"删除临时文件失败: {str(e)}")

    def rowCount(self, parent=QModelIndex()):
        """返回结果列表的行数"""
        return len(self.results)

    def data(self, index, role=Qt.DisplayRole):
        """返回指定索引的数据"""
        if not index.isValid() or not (0 <= index.row() < len(self.results)):
            return None
            
        result = self.results[index.row()]
        
        if role == Qt.DisplayRole:
            sampled_df = result['sampling_result'].get('sampled_df')
            total_points = len(result['sampling_result'].get('original_data', []))
            sampled_count = len(sampled_df) if sampled_df is not None else 0
            
            # 在Tab视图中，只显示project_name，不再显示sheet_name
            display_name = f"{result['project_name']}"
            
            # 如果是派生点集，添加父节点信息
            if result.get('is_dependent', False):
                parent_id = result.get('parent_id')
                if parent_id is not None and 0 <= parent_id < len(self.results):
                    parent = self.results[parent_id]
                    parent_name = parent['project_name']
                    
                    # 如果父点集和派生点集在不同的sheet中，则显示父点集的sheet名
                    if parent['sheet_name'] != result['sheet_name']:
                        display_name = f"{display_name} (派生自 {parent['sheet_name']}-{parent_name})"
                    else:
                        display_name = f"{display_name} (派生自 {parent_name})"
            
            return f"{display_name} [{sampled_count}/{total_points}]"
            
        elif role == Qt.ToolTipRole:
            if result.get('is_dependent', False):
                desc = result.get('dependency_description', '')
                parent_id = result.get('parent_id')
                if parent_id is not None and 0 <= parent_id < len(self.results):
                    parent = self.results[parent_id]
                    parent_name = parent['project_name']
                    parent_sheet = parent['sheet_name']
                    return f"派生点集 - {desc}\n父点集: {parent_sheet}-{parent_name}"
                return f"派生点集 - {desc}"
            return "独立点集"
            
        return None

    def add_result(self, sampling_result, params, sheet_name=None, project_name=None):
        """添加一个新的采样结果"""
        new_id = len(self.results)
        sampling_result['id'] = new_id
        new_entry = {
            'sampling_result': sampling_result,
            'sampling_numerical': params,
            'sheet_name': sheet_name or f"Sheet 1",
            'project_name': project_name or f"Default Project {new_id + 1}",
            'dependent_ids': [],  # 添加派生点集ID列表
            'is_dependent': False  # 标记是否为派生点集
        }
        self.beginInsertRows(QModelIndex(), new_id, new_id)
        self.results.append(new_entry)
        self.endInsertRows()
        
        # 如果添加了新的sheet名称，更新sheet_order
        sheet_name = new_entry['sheet_name']
        if sheet_name not in self.get_ordered_sheet_names():
            self.sheet_order.append(sheet_name)
            
        self.save_progress_to_file()  # 保存更改
        return new_id

    def add_dependent_result(self, parent_index, strategy, sheet_name=None):
        """添加派生于指定点集的结果
        
        Args:
            parent_index: 父点集索引
            strategy: 派生策略对象
            sheet_name: 派生点集的Sheet名，如不指定则使用父点集的Sheet名
            
        Returns:
            int: 新添加的派生点集索引
        """
        if not (0 <= parent_index < len(self.results)):
            logger.error(f"无效的父点集索引: {parent_index}")
            return -1
            
        try:
            # 获取父点集数据
            parent = self.results[parent_index]
            parent_result = parent['sampling_result']
            
            # 使用策略计算派生结果
            dependent_result = strategy.calculate(parent_result)
            
            # 设置派生点集基本信息
            new_id = len(self.results)
            dependent_result['id'] = new_id
            
            # 如果未指定sheet_name，则使用父点集的sheet_name
            if sheet_name is None:
                sheet_name = parent['sheet_name']
            
            # 创建派生点集条目，只存储必要的数据
            dependent_entry = {
                'sampling_result': dependent_result,
                'sampling_numerical': parent['sampling_numerical'],  # 继承父点集的数值参数
                'sheet_name': sheet_name,  # 使用指定的sheet_name或父点集的sheet_name
                'project_name': f"{parent['project_name']}",
                'parent_id': parent_index,  # 记录父点集ID
                'dependency_type': strategy.get_type(),  # 使用 get_type() 方法获取策略类型
                'dependency_description': strategy.get_description(),  # 存储策略描述
                'dependency_params': strategy.get_params(),  # 存储策略参数
                'is_dependent': True,  # 标记为派生点集
                'dependent_ids': []  # 初始化空派生列表
            }
            
            # 添加到结果集
            self.beginInsertRows(QModelIndex(), new_id, new_id)
            self.results.append(dependent_entry)
            self.endInsertRows()
            
            # 更新父点集的派生列表
            parent['dependent_ids'].append(new_id)
            
            # 如果添加了新的sheet名称，更新sheet_order
            if sheet_name not in self.get_ordered_sheet_names():
                self.sheet_order.append(sheet_name)
            
            self.save_progress_to_file()  # 保存更改
            return new_id
            
        except Exception as e:
            logger.error(f"创建派生点集失败: {str(e)}")
            return -1

    def update_result(self, index, sampling_result, params):
        """更新指定索引的采样结果并级联更新所有派生点集"""
        if 0 <= index < len(self.results):
            # 更新当前结果
            self.results[index]['sampling_result'] = sampling_result
            self.results[index]['sampling_numerical'] = params
            self.dataChanged.emit(self.index(index), self.index(index))
            
            # 更新所有派生结果
            self._update_dependent_results(index)
            
            self.save_progress_to_file()  # 保存更改

    def _update_dependent_results(self, parent_index):
        """递归更新所有派生于指定点集的结果"""
        if not (0 <= parent_index < len(self.results)):
            return
            
        parent = self.results[parent_index]
        parent_result = parent['sampling_result']
        
        # 遍历所有派生于此点集的ID
        for dep_id in parent.get('dependent_ids', []):
            if 0 <= dep_id < len(self.results):
                dependent = self.results[dep_id]
                
                # 跳过非派生点集（安全检查）
                if not dependent.get('is_dependent', False):
                    continue
                    
                # 获取派生类型和参数
                strategy_type = dependent.get('dependency_type')
                params = dependent.get('dependency_params', {}).copy()  # 创建副本以避免修改原始数据
                
                # 移除可能导致错误的参数（如'type'参数）
                if 'type' in params:
                    del params['type']
                
                if strategy_type:
                    try:
                        # 通过工厂创建策略对象
                        strategy = DerivativeStrategyFactory.create_strategy(strategy_type, **params)
                        
                        # 使用策略重新计算结果
                        new_result = strategy.calculate(parent_result)
                        new_result['id'] = dep_id
                        
                        # 更新结果
                        dependent['sampling_result'] = new_result
                        self.dataChanged.emit(self.index(dep_id), self.index(dep_id))
                        
                        # 递归更新派生于此点集的点集
                        self._update_dependent_results(dep_id)
                    except Exception as e:
                        logger.error(f"更新派生点集 {dep_id} 失败: {str(e)}")
                else:
                    logger.error(f"派生点集 {dep_id} 没有派生类型")

    def is_dependent_result(self, index):
        """检查指定索引的结果是否为派生点集"""
        if 0 <= index < len(self.results):
            return self.results[index].get('is_dependent', False)
        return False

    def get_result(self, index):
        """获取指定索引的采样结果"""
        return self.results[index]['sampling_result'] if 0 <= index < len(self.results) else None

    def get_sampling_params(self, index):
        """获取指定索引的采样参数"""
        return self.results[index]['sampling_numerical'] if 0 <= index < len(self.results) else None

    def set_names(self, index, sheet_name, project_name):
        """设置指定索引的Sheet名和备注"""
        if 0 <= index < len(self.results):
            old_sheet_name = self.results[index]['sheet_name']
            self.results[index]['sheet_name'] = sheet_name or "Sheet 1"
            self.results[index]['project_name'] = project_name
            self.dataChanged.emit(self.index(index), self.index(index))
            
            # 如果sheet名称发生变化且是新的sheet名称，则添加到sheet_order
            if old_sheet_name != sheet_name and sheet_name not in self.get_ordered_sheet_names():
                self.sheet_order.append(sheet_name)
                
            self.save_progress_to_file()  # 保存更改

    def get_sheet_name(self, index):
        """获取指定索引的Sheet名"""
        return self.results[index]['sheet_name'] if 0 <= index < len(self.results) else ""

    def get_project_name(self, index):
        """获取指定索引的备注"""
        return self.results[index]['project_name'] if 0 <= index < len(self.results) else ""

    def get_all_results(self):
        """获取所有采样结果"""
        return [result['sampling_result'] for result in self.results]

    def get_all_results_names(self, type_of_names, merge_list=None):
        """获取指定类型的所有名称（Sheet名或备注）"""
        names = []
        # 去重添加
        for result in self.results:
            if result[type_of_names] not in names:
                names.append(result[type_of_names])
        if merge_list is not None:
            names.extend(item for item in merge_list if item not in names)
        return names

    def removeRows(self, row, count, parent=QModelIndex()):
        if row < 0 or row >= self.rowCount():
            return False
            
        # 检查被删除的结果是否被其他结果派生
        result_to_remove = self.results[row]
        if result_to_remove.get('dependent_ids', []):
            # 获取所有派生于此结果的ID
            dependent_ids = result_to_remove.get('dependent_ids', []).copy()
            dependent_ids.sort(reverse=True)  # 从大到小排序，以便从后向前删除
            
            # 递归删除所有派生结果
            for dep_id in dependent_ids:
                if dep_id >= len(self.results):
                    continue
                self.removeRows(dep_id, 1)
        
        # 如果当前结果是派生点集，从父点集的派生列表中移除
        if result_to_remove.get('is_dependent', False):
            parent_id = result_to_remove.get('parent_id')
            if parent_id is not None and 0 <= parent_id < len(self.results):
                parent = self.results[parent_id]
                if row in parent.get('dependent_ids', []):
                    parent['dependent_ids'].remove(row)
        
        # 获取被删除的Sheet名
        removed_sheet_name = result_to_remove['sheet_name']
        
        # 执行删除操作
        self.beginRemoveRows(QModelIndex(), row, row + count - 1)
        del self.results[row:row + count]
        self.endRemoveRows()
        
        # 更新所有点集的ID引用
        self._update_ids_after_removal(row)
        
        # 检查是否还有该Sheet名的结果
        if not any(result['sheet_name'] == removed_sheet_name for result in self.results):
            # 如果没有，从sheet_order中移除
            if removed_sheet_name in self.sheet_order:
                self.sheet_order.remove(removed_sheet_name)
        
        self.save_progress_to_file()  # 保存更改
        return True

    def _update_ids_after_removal(self, removed_index):
        """更新删除点集后所有点集的ID引用"""
        # 更新所有结果的ID
        for i in range(removed_index, len(self.results)):
            self.results[i]['sampling_result']['id'] = i
            
        # 更新所有派生关系
        for i, result in enumerate(self.results):
            # 更新父ID引用
            if result.get('is_dependent', False):
                parent_id = result.get('parent_id')
                if parent_id is not None and parent_id >= removed_index:
                    result['parent_id'] = parent_id - 1
            
            # 更新派生ID列表
            dependent_ids = result.get('dependent_ids', [])
            for j, dep_id in enumerate(dependent_ids):
                if dep_id >= removed_index:
                    dependent_ids[j] = dep_id - 1

    def get_dependency_type(self, index):
        """获取指定索引结果的派生类型"""
        if 0 <= index < len(self.results):
            result = self.results[index]
            if result.get('is_dependent', False):
                # 直接从dependency_type字段获取类型
                return result.get('dependency_type')
        return None
        
    def get_dependency_params(self, index):
        """获取指定索引结果的派生参数"""
        if 0 <= index < len(self.results):
            result = self.results[index]
            if result.get('is_dependent', False):
                return result.get('dependency_params', {})
        return {}
        
    def update_dependency_params(self, index, new_params):
        """更新指定索引结果的派生参数"""
        if 0 <= index < len(self.results):
            result = self.results[index]
            if result.get('is_dependent', False):
                # 获取当前参数和类型
                current_params = result.get('dependency_params', {}).copy()  # 创建副本以避免修改原始数据
                strategy_type = result.get('dependency_type')
                
                if strategy_type:
                    # 更新参数
                    current_params.update(new_params)
                    result['dependency_params'] = current_params
                    
                    # 创建用于传递给策略构造函数的参数副本
                    constructor_params = current_params.copy()
                    # 移除可能导致错误的参数（如'type'参数）
                    if 'type' in constructor_params:
                        del constructor_params['type']
                    
                    # 通过工厂创建策略对象
                    strategy = DerivativeStrategyFactory.create_strategy(strategy_type, **constructor_params)
                    
                    # 更新描述
                    result['dependency_description'] = strategy.get_description()
                    
                    # 重新计算结果
                    self._update_dependent_results(result.get('parent_id'))
                    self.dataChanged.emit(self.index(index), self.index(index))
                    self.save_progress_to_file()
                else:
                    logger.error(f"派生点集 {index} 没有派生类型")

    def get_dependency_description(self, index):
        """获取指定索引结果的派生描述
        
        Args:
            index: 点集索引
            
        Returns:
            str: 派生描述文本，如果不是派生点集则返回空字符串
        """
        if 0 <= index < len(self.results):
            result = self.results[index]
            if result.get('is_dependent', False):
                return result.get('dependency_description', '')
        return ""

    def get_unique_sheet_names(self):
        """获取所有不重复的Sheet名称列表"""
        return sorted(set(result['sheet_name'] for result in self.results))
        
    def _generate_sheet_order(self):
        """根据当前结果生成Sheet顺序"""
        return list(self.get_unique_sheet_names())
        
    def get_ordered_sheet_names(self):
        """获取按顺序排列的Sheet名称列表"""
        # 先获取当前所有不重复的Sheet名称
        current_sheets = self.get_unique_sheet_names()
        
        # 构建一个完整的排序列表
        ordered_sheets = []
        
        # 首先添加在sheet_order中且仍然存在的sheet
        for sheet in self.sheet_order:
            if sheet in current_sheets:
                ordered_sheets.append(sheet)
                
        # 然后添加不在sheet_order中的新sheet
        for sheet in current_sheets:
            if sheet not in ordered_sheets:
                ordered_sheets.append(sheet)
                
        return ordered_sheets
        
    def reorder_sheets(self, new_order):
        """重新排序Sheet
        
        Args:
            new_order: 新的Sheet顺序，列表
        """
        # 记录操作
        logger.info(f"尝试重新排序Sheet: {new_order}")
        
        # 确保新的顺序包含所有当前的Sheet
        current_sheets = self.get_unique_sheet_names()
        logger.info(f"当前Sheet: {current_sheets}")
        
        if set(new_order) != set(current_sheets):
            logger.error(f"新的Sheet顺序不完整: 缺少 {set(current_sheets) - set(new_order)}")
            return False
            
        # 更新顺序
        self.sheet_order = new_order.copy()  # 使用副本以防传入的是引用
        logger.info(f"Sheet顺序已更新为: {self.sheet_order}")
        
        # 保存更改
        self.save_progress_to_file()
        return True
        
    def rename_sheet(self, old_name, new_name):
        """重命名Sheet
        
        Args:
            old_name: 旧的Sheet名称
            new_name: 新的Sheet名称
            
        Returns:
            bool: 重命名是否成功
        """
        # 检查新名称是否已存在
        if new_name in self.get_unique_sheet_names():
            logger.error(f"Sheet名称 {new_name} 已存在")
            return False
            
        # 更新所有相关结果的Sheet名称
        indices_to_update = []
        for i, result in enumerate(self.results):
            if result['sheet_name'] == old_name:
                result['sheet_name'] = new_name
                # 同时更新所有派生点集的sheet_name
                for dep_id in result.get('dependent_ids', []):
                    if 0 <= dep_id < len(self.results):
                        self.results[dep_id]['sheet_name'] = new_name
                indices_to_update.append(i)
                
        # 如果有更新，发出信号并更新sheet_order
        if indices_to_update:
            # 更新sheet_order
            if old_name in self.sheet_order:
                idx = self.sheet_order.index(old_name)
                self.sheet_order[idx] = new_name
                
            # 发出数据变更信号
            for i in indices_to_update:
                self.dataChanged.emit(self.index(i), self.index(i))
                
            self.save_progress_to_file()
            return True
        else:
            logger.error(f"未找到Sheet名称为 {old_name} 的结果")
            return False
            
    def create_sheet_proxy_model(self, sheet_name):
        """创建特定Sheet的代理模型
        
        Args:
            sheet_name: Sheet名称
            
        Returns:
            SheetProxyModel: 过滤后的代理模型
        """
        proxy_model = SheetProxyModel(sheet_name)
        proxy_model.setSourceModel(self)
        return proxy_model

    def move_item_in_sheet(self, source_index, direction):
        """在同一个Sheet内移动项目
        
        Args:
            source_index: 源模型中的行索引
            direction: 移动方向，-1表示上移，1表示下移
            
        Returns:
            bool: 移动是否成功
        """
        try:
            # 防御性参数检查
            if not isinstance(source_index, int) or not isinstance(direction, int):
                logger.error(f"移动项目参数类型错误: source_index={source_index}, direction={direction}")
                return False
                
            if source_index < 0 or source_index >= self.rowCount():
                logger.error(f"源索引无效: {source_index}")
                return False
                
            # 计算目标位置
            target_index = source_index + direction
            if target_index < 0 or target_index >= self.rowCount():
                logger.error(f"目标位置超出范围: {target_index}, 行数={self.rowCount()}")
                return False
                
            # 确保在同一个sheet内移动
            source_sheet = self.get_sheet_name(source_index)
            target_sheet = self.get_sheet_name(target_index)
            if source_sheet != target_sheet:
                logger.error(f"不能在不同的sheet之间移动: 源sheet={source_sheet}, 目标sheet={target_sheet}")
                return False
                
            logger.info(f"开始交换项目: 源位置={source_index}, 目标位置={target_index}")
            
            # 使用直接的数据操作，不调用moveRows，适用于所有项目
            # 通知视图开始布局变更
            self.layoutAboutToBeChanged.emit()
            
            # 备份要交换的数据
            source_data = self.results[source_index].copy()
            target_data = self.results[target_index].copy()
            
            # 直接交换数据
            self.results[source_index] = target_data
            self.results[target_index] = source_data
            
            # 确保ID正确
            if 'sampling_result' in self.results[source_index] and isinstance(self.results[source_index]['sampling_result'], dict):
                self.results[source_index]['sampling_result']['id'] = source_index
            
            if 'sampling_result' in self.results[target_index] and isinstance(self.results[target_index]['sampling_result'], dict):
                self.results[target_index]['sampling_result']['id'] = target_index
            
            # 更新依赖关系
            self._update_dependencies_after_swap(source_index, target_index)
            
            # 通知视图布局已变更
            self.layoutChanged.emit()
            
            # 保存变更
            self.save_progress_to_file()
            
            logger.info(f"交换项目成功: {source_index} <-> {target_index}")
            return True
            
        except Exception as e:
            logger.error(f"移动项目过程中发生错误: {str(e)}")
            return False

    def _update_dependencies_after_swap(self, pos1, pos2):
        """更新交换项目的引用关系"""
        try:
            # 防御性检查，确保索引有效
            if not (0 <= pos1 < len(self.results)) or not (0 <= pos2 < len(self.results)):
                logger.error(f"更新依赖关系时索引无效: pos1={pos1}, pos2={pos2}")
                return
                
            # 遍历所有结果，更新引用关系
            for i, result in enumerate(self.results):
                # 1. 更新父节点引用
                if result.get('is_dependent', False) and 'parent_id' in result:
                    parent_id = result.get('parent_id')
                    
                    # 安全地转换为整数
                    if parent_id is not None:
                        try:
                            parent_id = int(parent_id)
                            if parent_id == pos1:
                                result['parent_id'] = pos2
                            elif parent_id == pos2:
                                result['parent_id'] = pos1
                        except (ValueError, TypeError):
                            logger.error(f"无法将parent_id转换为整数: {parent_id}")
                
                # 2. 更新派生ID列表
                if 'dependent_ids' in result and isinstance(result['dependent_ids'], list):
                    dependent_ids = result['dependent_ids']
                    for j, dep_id in enumerate(dependent_ids):
                        # 安全地转换为整数
                        try:
                            dep_id = int(dep_id)
                            if dep_id == pos1:
                                dependent_ids[j] = pos2
                            elif dep_id == pos2:
                                dependent_ids[j] = pos1
                        except (ValueError, TypeError):
                            logger.error(f"无法将dependent_id转换为整数: {dep_id}")
                            
        except Exception as e:
            logger.error(f"更新引用关系时发生错误: {str(e)}")

    def flags(self, index):
        """返回项目标志"""
        if not index.isValid():
            return Qt.NoItemFlags
        # 仅支持选择和启用，不支持拖拽
        return Qt.ItemIsEnabled | Qt.ItemIsSelectable

    def mimeTypes(self):
        """返回支持的MIME类型（保留用于兼容性，但拖拽功能已禁用）"""
        return ['application/x-qabstractitemmodeldatalist']

    def mimeData(self, indexes):
        """创建拖放的MIME数据（保留用于兼容性，但拖拽功能已禁用）"""
        mime_data = QMimeData()
        encoded_data = QByteArray()
        stream = QDataStream(encoded_data, QIODevice.WriteOnly)

        # 写入版本信息
        stream.writeUInt32(QDataStream.Qt_4_0)
        
        # 写入行数
        rows = []
        for index in indexes:
            if index.isValid():
                rows.append(index.row())
        
        stream.writeUInt32(len(rows))
        for row in rows:
            stream.writeInt32(row)

        mime_data.setData('application/x-qabstractitemmodeldatalist', encoded_data)
        return mime_data

    def dropMimeData(self, data, action, row, column, parent):
        """处理拖放数据（已禁用，始终返回False）"""
        # 禁用拖放功能
        logger.info("拖放功能已禁用")
        return False
