import sys
import os
import tempfile
import datetime
from PySide6.QtWidgets import QApplication, QLabel, QComboBox, QLineEdit, QHBoxLayout, QWidget
from PySide6.QtCore import QTimer


def create_demo_app(app):
    # 创建主窗口
    main_window = QWidget()
    main_window.setWindowTitle("QHBoxLayout Demo")

    # 创建一个水平布局
    layout = QHBoxLayout()

    # 创建一个 QLabel
    label = QLabel("Label:")

    # 创建两个 QComboBox
    combo_box1 = QComboBox()
    combo_box1.addItems(["Option 1", "Option 2", "Option 3"])

    combo_box2 = QComboBox()
    combo_box2.addItems(["Choice A", "Choice B", "Choice C"])

    # 创建一个 QLineEdit
    line_edit = QLineEdit()
    line_edit.setPlaceholderText("Enter text here...")

    # 将控件添加到布局中
    layout.addWidget(label)
    layout.addWidget(combo_box1)
    layout.addWidget(combo_box2)
    layout.addWidget(line_edit)

    # 将布局设置到主窗口
    main_window.setLayout(layout)

    # 显示窗口
    main_window.resize(400, 100)
    main_window.show()

    return main_window


def handle_nuitka_splash_feedback():
    """处理 NUITKA 启动画面反馈文件"""
    print("\n[调试] *** 定时器触发回调函数 ***")
    print("[调试] 开始处理启动画面反馈文件，时间:", datetime.datetime.now())
    print("[调试] 检查NUITKA_ONEFILE_PARENT环境变量...")

    if "NUITKA_ONEFILE_PARENT" in os.environ:
        parent_id = os.environ["NUITKA_ONEFILE_PARENT"]
        print("[调试] 找到NUITKA_ONEFILE_PARENT环境变量，值为:", parent_id)

        splash_filename = os.path.join(
            tempfile.gettempdir(),
            f"onefile_{parent_id}_splash_feedback.tmp",
        )
        print("[调试] 启动画面反馈文件路径:", splash_filename)

        if os.path.exists(splash_filename):
            print("[调试] 启动画面反馈文件存在，正在删除...")
            try:
                os.unlink(splash_filename)
                print("[调试] 成功删除启动画面反馈文件")
            except Exception as e:
                print("[调试] 删除启动画面反馈文件时出错:", str(e))
        else:
            print("[调试] 启动画面反馈文件不存在")
    else:
        print("[调试] 未找到NUITKA_ONEFILE_PARENT环境变量")

    print("[调试] 处理完成，时间:", datetime.datetime.now())


# 全局定时器对象，防止被垃圾回收
_timer = None

def delayed_splash_feedback():
    """延迟5秒后执行handle_nuitka_splash_feedback函数"""
    global _timer
    print("[调试] 程序启动时间:", datetime.datetime.now())
    print("[调试] 设置5秒后执行handle_nuitka_splash_feedback函数...")

    # 创建一个QTimer，设置为单次触发，5000毫秒(5秒)后执行handle_nuitka_splash_feedback
    _timer = QTimer()
    _timer.setSingleShot(True)
    _timer.timeout.connect(handle_nuitka_splash_feedback)
    _timer.start(5000)  # 5000毫秒 = 5秒

    print("[调试] 定时器已启动，将在5秒后执行")


# 添加一个函数来检查定时器状态
def check_timer_status():
    global _timer
    if _timer is not None:
        print("[调试] 定时器状态检查:")
        print("[调试] - 定时器对象存在:", _timer is not None)
        print("[调试] - 定时器活动状态:", _timer.isActive())
        print("[调试] - 当前时间:", datetime.datetime.now())
    else:
        print("[调试] 定时器对象不存在")


if __name__ == "__main__":
    print("[调试] 程序开始执行")
    app = QApplication(sys.argv)

    # 启动延迟执行
    delayed_splash_feedback()

    # 创建并显示应用界面
    main_window = create_demo_app(app)

    # 在启动事件循环前检查定时器状态
    check_timer_status()

    # 启动应用程序事件循环
    sys.exit(app.exec())