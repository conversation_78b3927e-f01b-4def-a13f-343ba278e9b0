import time
import tempfile
import os

# 延迟启动5秒
print("Delaying startup by 5s...")
time.sleep(5)

# 使用此代码通知移除启动画面
if "NUITKA_ONEFILE_PARENT" in os.environ:
    splash_filename = os.path.join(
        tempfile.gettempdir(),
        "onefile_%d_splash_feedback.tmp" % int(os.environ["NUITKA_ONEFILE_PARENT"]),
    )

    if os.path.exists(splash_filename):
        os.unlink(splash_filename)

print("Done... splash should be gone.")

# 程序的其余部分从这里开始
print("Rest of your program goes here.")

input()