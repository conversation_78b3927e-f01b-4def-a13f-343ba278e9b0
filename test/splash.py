import time
import tempfile
import os
import datetime
import sys
import tkinter as tk
from tkinter import ttk
import threading


def remove_splash_file():
    """Check and remove the Nuitka splash feedback file if it exists"""
    if "NUITKA_ONEFILE_PARENT" in os.environ:
        parent_id = os.environ["NUITKA_ONEFILE_PARENT"]
        print(f"[INFO] Found NUITKA_ONEFILE_PARENT environment variable: {parent_id}")
        
        splash_filename = os.path.join(
            tempfile.gettempdir(),
            f"onefile_{parent_id}_splash_feedback.tmp",
        )
        print(f"[INFO] Splash feedback file path: {splash_filename}")

        if os.path.exists(splash_filename):
            print("[INFO] Splash feedback file exists, removing...")
            try:
                os.unlink(splash_filename)
                print("[INFO] Successfully removed splash feedback file")
                return True
            except Exception as e:
                print(f"[ERROR] Error removing splash feedback file: {str(e)}")
                return False
        else:
            print("[INFO] Splash feedback file does not exist")
            return False
    else:
        print("[INFO] NUITKA_ONEFILE_PARENT environment variable not found")
        return False


def console_mode():
    """Run in console mode - similar to the original implementation"""
    start_time = datetime.datetime.now()
    print(f"[DEBUG] Splash screen start time: {start_time}")
    print(f"[DEBUG] Temporary directory path: {tempfile.gettempdir()}")
    
    # Sleep for 5 seconds
    print("[DEBUG] Waiting for 5 seconds...")
    time_start = time.time()
    time.sleep(5)
    elapsed = time.time() - time_start
    print(f"[DEBUG] Actual sleep time: {elapsed:.2f} seconds")
    
    # Remove splash file
    remove_splash_file()
    
    end_time = datetime.datetime.now()
    print(f"[DEBUG] Completed... Splash screen should be gone, current time: {end_time}")
    print("Press Enter to continue...")
    input()


def gui_mode():
    """Run with a simple Tkinter GUI instead of console output"""
    # Create the main window
    root = tk.Tk()
    root.title("Splash Test")
    
    # Set window size and position it in the center of the screen
    window_width = 400
    window_height = 300
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x_position = (screen_width - window_width) // 2
    y_position = (screen_height - window_height) // 2
    root.geometry(f"{window_width}x{window_height}+{x_position}+{y_position}")
    
    # Create a frame to hold the content
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # Add a label for the title
    title_label = ttk.Label(main_frame, text="Nuitka Splash Test", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # Add a progress bar
    progress = ttk.Progressbar(main_frame, orient="horizontal", length=300, mode="determinate")
    progress.pack(pady=10)
    
    # Status label
    status_var = tk.StringVar(value="Initializing...")
    status_label = ttk.Label(main_frame, textvariable=status_var)
    status_label.pack(pady=10)
    
    # Log text area
    log_frame = ttk.LabelFrame(main_frame, text="Log")
    log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
    
    log_text = tk.Text(log_frame, height=8, width=40, wrap=tk.WORD)
    log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    # Add a scrollbar to the log text area
    scrollbar = ttk.Scrollbar(log_text, orient="vertical", command=log_text.yview)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    log_text.configure(yscrollcommand=scrollbar.set)
    
    # Close button (initially disabled)
    close_button = ttk.Button(main_frame, text="Close", command=root.destroy, state="disabled")
    close_button.pack(pady=10)
    
    def log(message):
        """Add a message to the log text area"""
        log_text.insert(tk.END, message + "\n")
        log_text.see(tk.END)  # Scroll to the end
    
    def update_progress(value):
        """Update the progress bar"""
        progress["value"] = value
        root.update_idletasks()
    
    def run_splash_test():
        """Run the splash test in a separate thread"""
        start_time = datetime.datetime.now()
        log(f"Splash screen start time: {start_time}")
        log(f"Temporary directory path: {tempfile.gettempdir()}")
        
        # Update status and progress
        status_var.set("Waiting for 5 seconds...")
        
        # Simulate progress over 5 seconds
        for i in range(101):
            time.sleep(0.05)  # 5 seconds total
            update_progress(i)
        
        # Try to remove the splash file
        status_var.set("Checking for splash feedback file...")
        result = remove_splash_file()
        
        # Log the results
        if result:
            log("Successfully removed splash feedback file")
        else:
            log("No splash feedback file was removed")
        
        end_time = datetime.datetime.now()
        log(f"Completed at: {end_time}")
        
        # Update UI
        status_var.set("Test completed")
        close_button.configure(state="normal")
    
    # Start the test in a separate thread
    threading.Thread(target=run_splash_test, daemon=True).start()
    
    # Start the main loop
    root.mainloop()


if __name__ == "__main__":
    # Check if we should use GUI mode or console mode
    gui_mode()
