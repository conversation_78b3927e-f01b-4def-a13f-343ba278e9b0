import openpyxl
import pandas as pd


def save_to_excel(filename, data_model):
    """
    将所有采样结果保存到Excel文件
    """
    try:
        # 创建一个新的工作簿
        workbook = openpyxl.Workbook()

        # 获取按顺序排列的Sheet名称
        sheet_names = data_model.get_ordered_sheet_names()

        # 为每个sheet名称创建一个工作表，并填充数据
        for sheet_name in sheet_names:
            sheet = workbook.create_sheet(title=sheet_name)
            populate_sheet(sheet, sheet_name, data_model)

        # 删除默认创建的空白工作表（如果有）
        if 'Sheet' in workbook.sheetnames:
            workbook.remove(workbook['Sheet'])

        # 保存工作簿到文件
        workbook.save(filename)
        return True, filename  # 返回成功状态和文件名

    except PermissionError:
        return False, "无权限保存，文件可能已被打开"
    except Exception as e:
        return False, f"保存失败：{e}"


def populate_sheet(sheet, sheet_name, data_model):
    """
    填充Excel工作表的内容
    """
    row = 1  # 从第一行开始写入数据
    for result_id, result in enumerate(data_model.get_all_results()):
        if data_model.get_sheet_name(result_id) == sheet_name:
            row = write_project_data(sheet, row, result_id, result, data_model)
            row += 1  # 添加一个空行分隔不同的项目


def write_project_data(sheet, row, result_id, result, data_model):
    """
    写入单个项目的数据
    """
    # 写入项目名称
    project_name = data_model.get_project_name(result_id)
    sheet.cell(row=row, column=1, value=project_name)
    row += 1

    # 获取采样数据
    sampled_df = result['sampled_df']

    # 重新排序列，将shot和panel列放在最前面
    headers = list(sampled_df.columns)
    shot_panel_cols = [col for col in headers if any(keyword in col.lower() for keyword in ['shot', 'panel'])]
    other_cols = [col for col in headers if col not in shot_panel_cols]
    reordered_headers = shot_panel_cols + other_cols
    sampled_df = sampled_df[reordered_headers]

    # 写入表头，首先添加 "NO." 列
    sheet.cell(row=row, column=1, value="NO.")
    for col, header in enumerate(reordered_headers, start=2):  # 从第二列开始
        sheet.cell(row=row, column=col, value=header)
    row += 1

    # 写入数据，添加编号
    for i, (_, row_data) in enumerate(sampled_df.iterrows(), start=1):
        sheet.cell(row=row, column=1, value=i)  # 写入编号
        for col, value in enumerate(row_data, start=2):  # 从第二列开始
            # 处理NA值，将其转换为None，这样在Excel中会显示为空单元格
            if pd.isna(value) or value is pd.NA:
                sheet.cell(row=row, column=col, value=None)
            else:
                sheet.cell(row=row, column=col, value=value)
        row += 1

    return row
