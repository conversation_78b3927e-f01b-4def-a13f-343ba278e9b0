import numpy as np

def edge_distribution_check(coords, threshold_multiplier=1.2, proportion_threshold=0.9):
    """识别坐标点是否近似分布在矩形四边附近."""

    # 拟合最小外接矩形
    min_x, min_y = np.min(coords, axis=0)
    max_x, max_y = np.max(coords, axis=0)

    # 计算矩形的周长和平均间距
    length = max_x - min_x  # 矩形长边
    width = max_y - min_y  # 矩形短边
    perimeter = 2 * (length + width)
    num_points = len(coords)
    avg_spacing = perimeter / num_points  # 平均间距
    threshold = threshold_multiplier * avg_spacing  # 设置距离阈值

    # 计算在阈值内的点比例
    near_count = 0
    for point in coords:
        px, py = point
        # 计算点到矩形边界的最短距离
        if px < min_x or px > max_x or py < min_y or py > max_y:
            dx = max(min_x - px, 0, px - max_x)  # 水平距离
            dy = max(min_y - py, 0, py - max_y)  # 垂直距离
            distance = np.sqrt(dx ** 2 + dy ** 2)
        else:
            dx = min(px - min_x, max_x - px)  # 点到左或右边界的最小距离
            dy = min(py - min_y, max_y - py)  # 点到上或下边界的最小距离
            distance = min(dx, dy)

        if distance < threshold:
            near_count += 1

    near_ratio = near_count / num_points  # 计算在矩形边附近的点比例
    return near_ratio >= proportion_threshold


def sampling_size_count(df_x: int, df_y: int, target_points: int, sampling_type: str, preference='Y'):
    x, y = 0, 0
    if preference and preference.upper() == 'X':
        increase_x = True
    else:
        increase_x = False

    if sampling_type == 'cc':
        target_points -= 4
    elif sampling_type == 'edge':
        df_x -= 2
        df_y -= 2
        target_points -= 4

    while True:
        if sampling_type == "edge":
            current_points = 2 * (x + y)
        else: # grid/grid_strict/cc
            current_points = x * y

        if current_points >= target_points:
            break

        if increase_x:
            if x < df_x:
                x += 1
            elif y < df_y:
                y += 1
            else:
                break
        else:
            if y < df_y:
                y += 1
            elif x < df_x:
                x += 1
            else:
                break

        increase_x = not increase_x  # 切换下一次增加的变量
    if sampling_type == 'edge':
        x += 2
        y += 2
    return x, y


def find_corner_points(coords, preference=None):
    # 找到最接近四个角的点
    if preference is None:
        top_left = coords[np.argmin(np.sum((coords - [min(coords[:, 0]), max(coords[:, 1])]) ** 2, axis=1))]
        top_right = coords[np.argmin(np.sum((coords - [max(coords[:, 0]), max(coords[:, 1])]) ** 2, axis=1))]
        bottom_left = coords[np.argmin(np.sum((coords - [min(coords[:, 0]), min(coords[:, 1])]) ** 2, axis=1))]
        bottom_right = coords[np.argmin(np.sum((coords - [max(coords[:, 0]), min(coords[:, 1])]) ** 2, axis=1))]
    elif preference == 'X':
        # 先找到Y坐标最小的点和最大的点的索引
        min_y_idx = np.argmin(coords[:, 1])
        max_y_idx = np.argmax(coords[:, 1])

        # 在Y最大的点中，取X最小的作为左上角，X最大的作为右上角
        top_points = coords[coords[:, 1] == coords[max_y_idx][1]]
        top_left = top_points[np.argmin(top_points[:, 0])]
        top_right = top_points[np.argmax(top_points[:, 0])]

        # 在Y最小的点中，取X最小的作为左下角，X最大的作为右下角
        bottom_points = coords[coords[:, 1] == coords[min_y_idx][1]]
        bottom_left = bottom_points[np.argmin(bottom_points[:, 0])]
        bottom_right = bottom_points[np.argmax(bottom_points[:, 0])]
    else:  # preference == 'Y'
        # 先找到X坐标最小的点和最大的点的索引
        min_x_idx = np.argmin(coords[:, 0])
        max_x_idx = np.argmax(coords[:, 0])

        # 在X最小的点中，取Y最大的作为左上角，Y最小的作为左下角
        left_points = coords[coords[:, 0] == coords[min_x_idx][0]]
        top_left = left_points[np.argmax(left_points[:, 1])]
        bottom_left = left_points[np.argmin(left_points[:, 1])]

        # 在X最大的点中，取Y最大的作为右上角，Y最小的作为右下角
        right_points = coords[coords[:, 0] == coords[max_x_idx][0]]
        top_right = right_points[np.argmax(right_points[:, 1])]
        bottom_right = right_points[np.argmin(right_points[:, 1])]
    return [top_left, top_right, bottom_left, bottom_right]