import io
import pandas as pd
import re


def _process_column(series: pd.Series, pattern: str):
    """处理单个列：只匹配第一个非空行作为列名，列名下只允许跳过连续空值，否则抛出异常"""
    # 找到第一个非空且非空字符串的行
    non_empty = series[series.notna() & (series.astype(str).str.strip() != "")]
    if non_empty.empty:
        return None, None, None

    # 获取第一个非空行的索引和值
    first_idx = non_empty.index[0]
    first_value = non_empty.iloc[0]

    # 检查该值是否匹配模式（忽略大小写）
    if not re.search(pattern, str(first_value), flags=re.IGNORECASE):
        return None, None, None

    # 确保列名为字符串并去除前后空白
    column_name = str(first_value).strip()

    # 从列名下一行开始提取数据
    data_start_idx = first_idx + 1
    data_series = series.iloc[data_start_idx:]

    # 允许前面是连续空值，跳过这些空值
    for offset, val in enumerate(data_series):
        if pd.notna(val) and str(val).strip() != "":
            data_start_idx += offset
            break
    else:
        # 如果全部是空值，就返回空数据
        return pd.Series([], dtype=series.dtype), first_idx, column_name

    # 检查在第一个非空值之后是否还存在空值，若有则抛异常
    remaining_data = series.iloc[data_start_idx:]
    if remaining_data.isna().any() or (remaining_data.astype(str).str.strip() == "").any():
        raise ValueError("数据中存在中断的空值")

    # 返回数据和信息
    return remaining_data.reset_index(drop=True), first_idx, column_name


def _find_shot_groups(df):
    """查找数据中的shot组"""
    shot_pattern = r'(?i)(?:shot|sheet)\s*\d+'
    shot_rows = []

    # 遍历所有列寻找shot标识
    for col in df.columns:
        series = df[col].astype(str)
        shot_matches = series.str.contains(shot_pattern, case=False, regex=True, na=False)
        if shot_matches.any():
            row_idx = shot_matches.idxmax()
            shot_rows.append((row_idx, col))

    return shot_rows


def _process_multiple_groups(df, shot_groups):
    """处理多组数据格式，支持不等长数据"""
    all_data = []
    x_col_name = 'X'
    y_col_name = 'Y'
    shot_id_col_name = 'Shot ID'

    # 处理每个shot组
    for row_idx, col_idx in shot_groups:
        # 获取shot ID
        shot_id = df.iloc[row_idx, col_idx]
        if pd.isna(shot_id):
            continue

        # 提取shot编号
        shot_number = shot_id.lower().replace('shot', '').strip()

        # X和Y列的位置
        x_col = col_idx
        y_col = col_idx + 1 if col_idx + 1 < len(df.columns) else None

        if y_col is not None:
            # 找到X行（在shot ID行的下一行）
            x_row = row_idx + 1
            if x_row < len(df) and str(df.iloc[x_row, x_col]) == 'X':
                # 数据开始行
                data_start = x_row + 1

                # 提取该组的所有数据
                x_values = []
                y_values = []

                for i in range(data_start, len(df)):
                    x_val = df.iloc[i, x_col]
                    y_val = df.iloc[i, y_col]

                    if pd.isna(x_val) or pd.isna(y_val):
                        continue

                    try:
                        x_values.append(float(x_val))
                        y_values.append(float(y_val))
                    except (ValueError, TypeError):
                        continue

                # 只有在有有效数据时才添加到结果中
                if x_values and y_values:
                    # 为每个数据点创建相同的shot ID
                    shot_ids = [shot_number] * len(x_values)

                    group_data = pd.DataFrame({
                        shot_id_col_name: shot_ids,
                        x_col_name: x_values,
                        y_col_name: y_values
                    })

                    all_data.append(group_data)

    # 合并所有组的数据
    if all_data:
        final_df = pd.concat(all_data, ignore_index=True)
        # 如果有重复坐标，抛异常
        if final_df.duplicated(subset=[x_col_name, y_col_name]).any():
            same_coords = final_df[final_df.duplicated(subset=[x_col_name, y_col_name], keep=False)]
            first_same_coord = [float(same_coords.iloc[0][x_col_name]), float(same_coords.iloc[0][y_col_name])]
            raise ValueError(f"存在重复坐标，请检查 ({first_same_coord[0]}, {first_same_coord[1]})")
        return final_df, final_df.columns.tolist(), x_col_name, y_col_name, shot_id_col_name

    return None


def parse_clipboard_data(text):
    """主函数：解析剪贴板数据"""
    # 读取数据，不使用标题行
    original_df = pd.read_csv(io.StringIO(text), sep='\t', header=None, engine='python')

    # 查找是否为横排 Shot ID
    shot_groups = _find_shot_groups(original_df)
    if shot_groups:
        result = _process_multiple_groups(original_df, shot_groups)
        if result is not None:
            return result

    # 如果不为横排 Shot ID

    # 定义映射表
    title_map = {
        'x_col': r"(?i)^X.*",  # 匹配 'X' 开头，不区分大小写
        'y_col': r"(?i)^Y.*",  # 匹配 'Y' 开头，不区分大小写
        'shot_id_col': r"(?i).*shot.*|.*sheet.*"  # 包含 'Shot' 或 'Sheet'，不区分大小写
    }

    # 初始化结果数据结构
    new_df = pd.DataFrame()
    columns = {}
    column_data = {}  # 存储每列的数据和列名

    # 预处理：如果某列的起始n个单元格为空，则它上一列的前n个单元格也置空
    for col_idx in range(1, original_df.shape[1]):  # 从第二列开始
        current_col = original_df.iloc[:, col_idx]

        # 找出当前列开头连续的空值数量
        empty_count = 0
        for i, val in enumerate(current_col):
            if pd.isna(val) or val == '':
                empty_count += 1
            else:
                break

        # 如果有空值，将上一列相应位置也设为空值
        if empty_count > 0:
            for i in range(empty_count):
                original_df.iloc[i, col_idx-1] = ''

    # 使用第一列作为参考列来划分表格
    if original_df.shape[1] > 0 and original_df.shape[0] > 0:
        first_col = original_df.iloc[:, 0]

        # 找到第一列中第二个非空单元格的位置
        # 这将用于划分标题区域和数据区域
        non_empty_indices = first_col[first_col.notna() & (first_col.astype(str).str.strip() != "")].index

        if len(non_empty_indices) >= 2:
            # 第二个非空单元格位置作为数据开始行
            data_start_idx = non_empty_indices[1]
            # 处理每一列
            for col in original_df.columns:
                current_col = original_df.iloc[:, col]
                col_not_empty_indices = current_col[current_col.notna() & (current_col.astype(str).str.strip() != "")].index
                # 第一个非空单元格位置作为标题行
                title_row_idx = col_not_empty_indices[0]

                # 获取当前列的标题
                title_value = original_df.iloc[title_row_idx, col]

                # 如果标题为空，跳过该列
                if pd.isna(title_value) or str(title_value).strip() == "":
                    continue

                # 将标题转换为字符串
                column_name = str(title_value)
                if column_name in column_data:
                    suffix = 2
                    while f"{column_name}_{suffix}" in column_data:
                        suffix += 1
                    column_name = f"{column_name}_{suffix}"

                # 提取数据部分
                data = original_df.iloc[data_start_idx:, col]

                # 如果数据全空，则跳过
                if data.isna().all() or (data.astype(str).str.strip() == "").all():
                    continue

                # 检查该列是否为关键列（匹配 title_map 中的模式）
                is_key_column = False
                matched_key = None

                for key, pattern in title_map.items():
                    if re.search(pattern, column_name, re.IGNORECASE):
                        is_key_column = True
                        matched_key = key
                        break

                if is_key_column and matched_key not in columns: # 仅处理首次出现的关键列
                    # 检查数据中是否有空值
                    empty_indices = data[data.isna() | (data.astype(str).str.strip() == "")].index
                    if not empty_indices.empty:
                        first_empty_idx = empty_indices[0]
                        raise ValueError(f"关键列 '{column_name}' 第{first_empty_idx - data_start_idx + 1}行值为空")

                    # 将列名添加到关键列映射中
                    columns[matched_key] = column_name

                # 将非空数据添加到结果中
                valid_data = data.dropna().reset_index(drop=True)
                if not valid_data.empty:
                    column_data[column_name] = valid_data

            # 确保所有列的数据长度一致
            # 找出最长的数据列
            max_length = 0
            for data in column_data.values():
                max_length = max(max_length, len(data))

            # 将所有列的数据填充到相同长度
            for col_name, data in column_data.items():
                data_length = len(data)
                if data_length < max_length:
                    # 用NaN填充不足的部分
                    padding = pd.Series([pd.NA] * (max_length - data_length))
                    column_data[col_name] = pd.concat([data, padding]).reset_index(drop=True)

                # 将处理后的数据添加到新DataFrame
                new_df[col_name] = column_data[col_name]

    # 尝试将 X 和 Y 列转换为数值类型
    for col in [columns.get('x_col'), columns.get('y_col')]:
        if col in new_df.columns:
            new_df[col] = pd.to_numeric(new_df[col], errors='coerce')

    x_col = columns.get('x_col')
    y_col = columns.get('y_col')
    shot_id_col = columns.get('shot_id_col')

    # 对非XY列进行类型推断
    for col in new_df.columns:
        if col not in [x_col, y_col]:
            # 尝试转换为数值类型
            try:
                numeric_series = pd.to_numeric(new_df[col], errors='raise')
                # 如果成功转换为数值，再判断是否应该是整数
                if numeric_series.equals(numeric_series.astype(int)):
                    new_df[col] = numeric_series.astype(int)
                else:
                    new_df[col] = numeric_series
            except (ValueError, TypeError):
                # 如果无法转换为数值，保持原样
                pass

    # 如果存在有效数据，进行去重处理后返回结果
    if not new_df.empty and x_col and y_col:
        # 如果有重复项，抛异常
        if new_df.duplicated(subset=[x_col, y_col]).any():
            same_coords = new_df[new_df.duplicated(subset=[x_col, y_col], keep=False)]
            first_same_coord = [float(same_coords.iloc[0][x_col]), float(same_coords.iloc[0][y_col])]
            raise ValueError(f"存在重复坐标，请检查 ({first_same_coord[0]}, {first_same_coord[1]})")
        return new_df, new_df.columns.tolist(), x_col, y_col, shot_id_col

    # 如果所有处理方式都失败，返回空结果
    return pd.DataFrame(), [], None, None, None
