from utils.clipboard_listener import ClipboardListener


class MainClipboardListener(ClipboardListener):
    """主控制器的剪贴板监听器"""
    
    def __init__(self, main_controller, parent=None):
        super().__init__(parent)
        self.main_controller = main_controller
        
        # 连接信号到主控制器的处理方法
        self.data_processed.connect(self.main_controller.handle_clipboard_data)
        self.status_changed.connect(self.main_controller.set_status)