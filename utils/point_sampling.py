# point_sampling.py
import random

import numpy as np
from utils.cKDTree import cKDTree

from utils.point_tools import find_corner_points


def grid_selection(coords, method='grid', sampling_size=None, sampling_area=None, cancel_checker=None):
    coords = np.array(coords)  # 转换为 NumPy 数组

    # 检查取消请求
    if cancel_checker and cancel_checker.check_cancelled():
        return None

    grid_x, grid_y = sampling_size
    total_grids = grid_x * grid_y
    if len(coords) <= total_grids:
        return coords
    elif total_grids <= 0:
        return np.empty((0, 2))

    coord_xy = coords[:, :2]  # 只取前两列作为 x 和 y 坐标

    # 检查取消请求
    if cancel_checker and cancel_checker.check_cancelled():
        return None

    if method == 'edge':
        min_x, min_y = np.min(sampling_area, axis=0)
        max_x, max_y = np.max(sampling_area, axis=0)
        grid_width = ((max_x - min_x) / (grid_x + 1)) if grid_x != 0 else 0
        grid_height = ((max_y - min_y) / (grid_y + 1)) if grid_y != 0 else 0
    elif method == 'grid':
        min_x, min_y = np.min(coord_xy, axis=0)
        max_x, max_y = np.max(coord_xy, axis=0)
        # 计算网格宽度和高度，使得外圈网格中心恰好落在坐标最外围
        grid_width = (max_x - min_x) / max(1, grid_x - 1)
        grid_height = (max_y - min_y) / max(1, grid_y - 1)
    else:
        # method == 'cc'
        min_x, min_y = np.min(coord_xy, axis=0)
        max_x, max_y = np.max(coord_xy, axis=0)
        grid_width = (max_x - min_x) / grid_x
        grid_height = (max_y - min_y) / grid_y

    # 引入微扰，避免点位距离相同
    min_x_fixed = min_x + random.random() / 10
    min_y_fixed = min_y + random.random() / 10

    def get_offset(k, grid_size, grid_num):
        if method == 'grid':
            if grid_num == 1:
                offset = 0.5 * grid_size
            else:
                offset = k * grid_size
        elif method == 'cc':
            offset = (k + 0.5) * grid_size
        else: # method == 'edge'
            if grid_num == 1:
                offset = grid_size
            else:
                offset = (k + 1) * grid_size

        return offset

    grid_centers = [
        [min_x_fixed + get_offset(i, grid_width, grid_x),
         min_y_fixed + get_offset(j, grid_height, grid_y)]
        for i in range(grid_x) for j in range(grid_y)
    ]

    # 检查取消请求
    if cancel_checker and cancel_checker.check_cancelled():
        return None

    tree = cKDTree(coord_xy)  # 只在 x 和 y 坐标上构建 KD 树
    selected_coords = []

    mask = np.ones(len(coord_xy), dtype=bool)
    for center in grid_centers:
        # 定期检查取消请求
        if cancel_checker and cancel_checker.check_cancelled():
            return None

        distances, indices = tree.query(center, k=len(coord_xy))
        nearest_unselected = indices[mask[indices]][0]
        selected_coords.append(coords[nearest_unselected])
        mask[nearest_unselected] = False

    return np.array(selected_coords)


def grid_strict_selection(coords, prefer, sampling_size, cancel_checker=None):
    """
    在给定坐标集合中选择指定数量的点，使其保持横竖对齐且尽可能均匀分布
    """
    # 检查取消请求
    if cancel_checker and cancel_checker.check_cancelled():
        return None

    def is_valid_point(x, y):
        """检查坐标点是否有效"""
        return (x, y) in coords_set

    def find_valid_points_near(target, available_coords, max_candidates=5):
        """找到靠近目标值的多个有效候选点"""
        distances = np.abs(available_coords - target)
        candidates = []
        for idx in np.argsort(distances):
            if len(candidates) >= max_candidates:
                break
            candidates.append(available_coords[idx])
        return candidates

    def verify_grid(x_coords, y_coords):
        """验证给定的x和y坐标是否能形成有效网格"""
        for x in x_coords:
            for y in y_coords:
                if not is_valid_point(x, y):
                    return False
        return True

    def get_ideal_positions(min_val, max_val, count):
        """获取理想的均匀分布位置"""
        if count == 1:
            return [min_val]
        return [min_val + (max_val - min_val) * i / (count - 1) for i in range(count)]

    def build_grid_recursive(x_remaining, y_remaining, curr_x, curr_y, ideal_x, ideal_y):
        """递归构建网格，允许回溯"""
        # 检查取消请求
        if cancel_checker and cancel_checker.check_cancelled():
            return None

        def try_add_coord(is_x_axis):
            """尝试添加一个坐标（X轴或Y轴）"""
            remaining = x_remaining if is_x_axis else y_remaining
            if remaining <= 0:
                return None

            curr = curr_x if is_x_axis else curr_y
            ideal = ideal_x if is_x_axis else ideal_y
            available = unique_x if is_x_axis else unique_y

            target = ideal[len(curr)]
            candidates = find_valid_points_near(target, available)

            for val in candidates:
                # 检查取消请求
                if cancel_checker and cancel_checker.check_cancelled():
                    return None

                if val not in curr:
                    # 验证新坐标是否与已有坐标兼容
                    if all(is_valid_point(val, y) for y in curr_y) if is_x_axis else \
                            all(is_valid_point(x, val) for x in curr_x):
                        curr.append(val)
                        result = build_grid_recursive(
                            x_remaining - (1 if is_x_axis else 0),
                            y_remaining - (0 if is_x_axis else 1),
                            curr_x, curr_y, ideal_x, ideal_y
                        )
                        if result is not None:
                            return result
                        curr.pop()
            return None

        # 如果已经选够了所有点，验证并返回结果
        if x_remaining == 0 and y_remaining == 0:
            if verify_grid(curr_x, curr_y):
                return curr_x.copy(), curr_y.copy()
            return None

        # 根据prefer参数决定搜索顺序
        first_axis = prefer == 'Y'
        result = try_add_coord(first_axis) or try_add_coord(not first_axis)
        return result

    # 输入检查与初始化
    x_size, y_size = sampling_size

    coords = np.array(coords)
    coords_set = set(map(tuple, coords[:, :2]))

    unique_x = sorted(list(set(coords[:, 0])))
    unique_y = sorted(list(set(coords[:, 1])))

    if x_size > len(unique_x) or y_size > len(unique_y):
        return np.empty((0, 2))

    # 预计算理想位置
    ideal_x = get_ideal_positions(min(unique_x), max(unique_x), x_size)
    ideal_y = get_ideal_positions(min(unique_y), max(unique_y), y_size)

    # 尝试构建网格
    result = build_grid_recursive(x_size, y_size, [], [], ideal_x, ideal_y)

    if result is not None:
        x_coords, y_coords = result
        return np.array([(x, y) for x in x_coords for y in y_coords])

    return np.empty((0, 2))


def edge_selection(coords, preference=None, sampling_size=None, cancel_checker=None):
    # 检查取消请求
    if cancel_checker and cancel_checker.check_cancelled():
        return None

    corner_points = find_corner_points(coords, preference)

    points_x_edge, points_y_edge = sampling_size
    points_x_edge -= 2
    points_y_edge -= 2

    if 2 * (points_x_edge + points_y_edge) + 4 >= len(coords):
        return np.array(coords)
    top_left, top_right, bottom_left, bottom_right = corner_points

    # 检查取消请求
    if cancel_checker and cancel_checker.check_cancelled():
        return None

    # 创建一个函数来检查点是否在corner_points中
    def not_in_corner_points(point):
        return not np.any(np.all(corner_points == point, axis=1))

    # 选择上边的点
    coords_top = coords[(coords[:, 1] >= min(top_left[1], top_right[1])) &
                        (np.apply_along_axis(not_in_corner_points, 1, coords))]
    top_points = grid_selection(coords_top, method='edge', sampling_size=(points_x_edge, 1),
                                sampling_area=(top_left, top_right), cancel_checker=cancel_checker)
    if top_points is None:
        return None

    # 选择下边的点
    coords_bottom = coords[(coords[:, 1] <= max(bottom_left[1], bottom_right[1])) &
                           (np.apply_along_axis(not_in_corner_points, 1, coords))]
    bottom_points = grid_selection(coords_bottom, method='edge', sampling_size=(points_x_edge, 1),
                                   sampling_area=(bottom_left, bottom_right), cancel_checker=cancel_checker)
    if bottom_points is None:
        return None

    # 选择左边的点
    coords_left = coords[(coords[:, 0] <= max(top_left[0], bottom_left[0])) &
                         (np.apply_along_axis(not_in_corner_points, 1, coords))]
    left_points = grid_selection(coords_left, method='edge', sampling_size=(1, points_y_edge),
                                 sampling_area=(top_left, bottom_left), cancel_checker=cancel_checker)
    if left_points is None:
        return None

    # 选择右边的点
    coords_right = coords[(coords[:, 0] >= min(top_right[0], bottom_right[0])) &
                          (np.apply_along_axis(not_in_corner_points, 1, coords))]
    right_points = grid_selection(coords_right, method='edge', sampling_size=(1, points_y_edge),
                                  sampling_area=(top_right, bottom_right), cancel_checker=cancel_checker)
    if right_points is None:
        return None

    # 合并所有选择的点
    selected_points = np.vstack([corner_points, top_points, bottom_points, left_points, right_points])

    return selected_points


def cc_selection(coords, preference=None, sampling_size=None, cancel_checker=None):
    # 检查取消请求
    if cancel_checker and cancel_checker.check_cancelled():
        return None

    def not_in_corner_points(point):
        return not np.any(np.all(corner_points == point, axis=1))

    corner_points = find_corner_points(coords, preference)
    coords_center = coords[(np.apply_along_axis(not_in_corner_points, 1, coords))]
    center_points = grid_selection(coords_center, 'cc', sampling_size, cancel_checker=cancel_checker)
    if center_points is None:
        return None

    selected_points = np.vstack([corner_points, center_points])

    return selected_points
