import pandas as pd

from utils.derivative_strategies.derivative_base import DerivativeStrategy


class PpmExpansionStrategy(DerivativeStrategy):
    """外扩点集策略，支持X和Y方向独立设置ppm值"""
    strategy_type = "ppm_expansion"  # 策略类型标识符

    def __init__(self, x_ppm_value, y_ppm_value):
        self.x_ppm_value = x_ppm_value
        self.y_ppm_value = y_ppm_value

    def calculate(self, parent_result):
        """计算外扩点集结果

        对源点集中的每个点(x, y)，生成新的坐标：
        x_new = x * (1 + x_ppm * 1e-6)
        y_new = y * (1 + y_ppm * 1e-6)
        """
        # 复制父结果的基本信息
        result = parent_result.copy()

        # 获取原始采样数据
        sampled_df = parent_result.get('sampled_df')
        x_col_name = parent_result.get('x_col_name')
        y_col_name = parent_result.get('y_col_name')

        if sampled_df is None or x_col_name is None or y_col_name is None:
            raise ValueError("父点集缺少必要的数据")

        # 复制采样数据
        expanded_df = sampled_df.copy()

        # 确保列为数值类型
        expanded_df[x_col_name] = pd.to_numeric(expanded_df[x_col_name], errors='coerce')
        expanded_df[y_col_name] = pd.to_numeric(expanded_df[y_col_name], errors='coerce')

        # 计算新坐标
        x_scale = 1 + self.x_ppm_value * 1e-6
        y_scale = 1 + self.y_ppm_value * 1e-6

        expanded_df[x_col_name] = expanded_df[x_col_name] * x_scale
        expanded_df[y_col_name] = expanded_df[y_col_name] * y_scale

        # 更新结果
        result['sampled_df'] = expanded_df

        return result

    def get_description(self):
        """返回策略描述"""
        if self.x_ppm_value == self.y_ppm_value:
            return f"外扩 {self.x_ppm_value}ppm"
        return f"外扩 X:{self.x_ppm_value}ppm Y:{self.y_ppm_value}ppm"

    def get_params(self):
        """返回序列化参数"""
        return {
            "type": self.strategy_type,
            "x_ppm_value": self.x_ppm_value,
            "y_ppm_value": self.y_ppm_value
        }
