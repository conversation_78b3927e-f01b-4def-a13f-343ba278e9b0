from typing import Dict, Type

from utils.derivative_strategies.derivative_base import DerivativeStrategy
from utils.derivative_strategies.strategy_ppm_expansion import PpmExpansionStrategy
from utils.derivative_strategies.strategy_rotation import RotationStrategy
from utils.derivative_strategies.strategy_nearest_neighbor import NearestNeighborStrategy


class DerivativeStrategyFactory:
    """派生策略工厂类 - 处理所有策略的注册和实例化"""
    _strategies: Dict[str, Type["DerivativeStrategy"]] = {}
    _dialogs: Dict[str, str] = {}
    _icons: Dict[str, str] = {}
    _menu_texts: Dict[str, str] = {}
    
    @classmethod
    def register_strategy(cls, strategy_type: str, strategy_class: Type["DerivativeStrategy"],
                          dialog_class=None, icon_path=None, menu_text=None):
        """注册一个策略类及其相关组件
        
        Args:
            strategy_type: 策略类型标识符
            strategy_class: 策略类
            dialog_class: 对话框类名称 (字符串)
            icon_path: 图标路径
            menu_text: 菜单显示文本
        """
        cls._strategies[strategy_type] = strategy_class
        if dialog_class:
            cls._dialogs[strategy_type] = dialog_class
        if icon_path:
            cls._icons[strategy_type] = icon_path
        if menu_text:
            cls._menu_texts[strategy_type] = menu_text
    
    @classmethod
    def create_strategy(cls, strategy_type: str, **params):
        """创建指定类型的策略实例
        
        Args:
            strategy_type: 策略类型标识符
            **params: 策略构造函数参数
            
        Returns:
            DerivativeStrategy: 创建的策略实例
        """
        if strategy_type not in cls._strategies:
            raise ValueError(f"未注册的策略类型: {strategy_type}")
        return cls._strategies[strategy_type](**params)
    
    @classmethod
    def get_dialog_class(cls, strategy_type: str):
        """获取指定策略类型的对话框类名称"""
        return cls._dialogs.get(strategy_type)
    
    @classmethod
    def get_icon_path(cls, strategy_type: str):
        """获取指定策略类型的图标路径"""
        return cls._icons.get(strategy_type)
    
    @classmethod
    def get_menu_text(cls, strategy_type: str):
        """获取指定策略类型的菜单文本"""
        return cls._menu_texts.get(strategy_type)
    
    @classmethod
    def get_all_strategy_types(cls):
        """获取所有已注册的策略类型"""
        return list(cls._strategies.keys())


# 注册所有策略

# 注册外扩策略
DerivativeStrategyFactory.register_strategy(
    "ppm_expansion",
    PpmExpansionStrategy,
    dialog_class="DialogExpansion",
    icon_path="resources/外扩.svg",
    menu_text="外扩"
)

# 注册旋转策略
'''DerivativeStrategyFactory.register_strategy(
    "rotation",
    RotationStrategy,
    dialog_class="DialogRotation",
    icon_path="resources/旋转.png",
    menu_text="旋转"
)'''

# 注册近邻策略
DerivativeStrategyFactory.register_strategy(
    "nearest_neighbor",
    NearestNeighborStrategy,
    dialog_class="DialogNearestNeighbor",
    icon_path="resources/近邻.svg",
    menu_text="近邻"
)