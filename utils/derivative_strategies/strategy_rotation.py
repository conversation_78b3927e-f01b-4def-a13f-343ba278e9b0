import math

import pandas as pd

from utils.derivative_strategies.derivative_base import DerivativeStrategy


class RotationStrategy(DerivativeStrategy):
    """旋转变换策略，支持以原点为中心进行旋转"""
    strategy_type = "rotation"  # 策略类型标识符

    def __init__(self, angle_degrees):
        self.angle_degrees = angle_degrees

    def calculate(self, parent_result):
        """计算旋转后的点集

        将源点集中的每个点(x, y)绕原点旋转指定角度
        """
        # 复制父结果的基本信息
        result = parent_result.copy()

        # 获取原始采样数据
        sampled_df = parent_result.get('sampled_df')
        x_col_name = parent_result.get('x_col_name')
        y_col_name = parent_result.get('y_col_name')

        if sampled_df is None or x_col_name is None or y_col_name is None:
            raise ValueError("父点集缺少必要的数据")

        # 复制采样数据
        rotated_df = sampled_df.copy()

        # 确保列为数值类型
        rotated_df[x_col_name] = pd.to_numeric(rotated_df[x_col_name], errors='coerce')
        rotated_df[y_col_name] = pd.to_numeric(rotated_df[y_col_name], errors='coerce')

        # 计算旋转
        angle_rad = math.radians(self.angle_degrees)
        cos_angle = math.cos(angle_rad)
        sin_angle = math.sin(angle_rad)

        # 执行旋转变换
        x_values = rotated_df[x_col_name].values
        y_values = rotated_df[y_col_name].values

        rotated_df[x_col_name] = x_values * cos_angle - y_values * sin_angle
        rotated_df[y_col_name] = x_values * sin_angle + y_values * cos_angle

        # 更新结果
        result['sampled_df'] = rotated_df

        return result

    def get_description(self):
        """返回策略描述"""
        return f"旋转 {self.angle_degrees}°"

    def get_params(self):
        """返回序列化参数"""
        return {
            "type": self.strategy_type,
            "angle_degrees": self.angle_degrees
        }
