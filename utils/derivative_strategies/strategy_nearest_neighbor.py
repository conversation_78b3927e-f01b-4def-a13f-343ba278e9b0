import numpy as np
import pandas as pd

from utils.derivative_strategies.derivative_base import DerivativeStrategy


class NearestNeighborStrategy(DerivativeStrategy):
    """近邻策略，为每个父坐标找到四边形内最近的点"""
    strategy_type = "nearest_neighbor"  # 策略类型标识符

    def __init__(self, quadrilateral_points):
        """初始化近邻策略
        
        Args:
            quadrilateral_points: 多个四边形的角点坐标，格式为[{'points': [[x1,y1], ...], 'panel_name': 'name'}, ...]
        """
        # 对每个四边形的角点进行排序，确保形成简单多边形
        self.quadrilateral_points = []
        self.panel_names = []  # 存储panel名称
        
        for quad_dict in quadrilateral_points:
            # 获取点坐标和panel名称
            if isinstance(quad_dict, dict) and 'points' in quad_dict:
                points = quad_dict['points']
                panel_name = quad_dict.get('panel_name', '')
            else:
                # 向后兼容，处理旧格式
                points = quad_dict
                panel_name = f"四边形 #{len(self.quadrilateral_points)+1}"
                
            # 确保四边形有足够的点
            if len(points) >= 3:
                sorted_quad = self._sort_quadrilateral_points(np.array(points))
                self.quadrilateral_points.append(sorted_quad)
                self.panel_names.append(panel_name)
        
    def _sort_quadrilateral_points(self, quad):
        """对四边形角点进行排序，确保形成简单多边形
        
        使用凸包算法对点进行排序，如果四边形不是凸的，至少确保它是简单的（不自相交）
        
        Args:
            quad: 四边形的角点坐标，形状为(n, 2)，其中n>=3
            
        Returns:
            numpy.ndarray: 排序后的四边形角点坐标，形状为(n, 2)
        """
        # 如果点数不足，直接返回
        if len(quad) < 3:
            return quad
            
        # 计算质心
        centroid = np.mean(quad, axis=0)
        
        # 按照与质心的连线相对于x轴的角度排序（逆时针）
        def angle_with_centroid(point):
            return np.arctan2(point[1] - centroid[1], point[0] - centroid[0])
            
        # 排序点
        sorted_indices = np.argsort([angle_with_centroid(p) for p in quad])
        sorted_quad = quad[sorted_indices]
        
        return sorted_quad
        
    def calculate(self, parent_result):
        """计算近邻点集结果
        
        为父点集中的每个点找到四边形内最近的点
        """
        # 复制父结果的基本信息
        result = parent_result.copy()

        # 获取原始采样数据
        sampled_df = parent_result.get('sampled_df')
        x_col_name = parent_result.get('x_col_name')
        y_col_name = parent_result.get('y_col_name')

        if sampled_df is None or x_col_name is None or y_col_name is None:
            raise ValueError("父点集缺少必要的数据")

        # 确保列为数值类型
        sampled_df[x_col_name] = pd.to_numeric(sampled_df[x_col_name], errors='coerce')
        sampled_df[y_col_name] = pd.to_numeric(sampled_df[y_col_name], errors='coerce')
        
        # 检查是否有有效的四边形
        if not self.quadrilateral_points:
            raise ValueError("没有有效的四边形数据")
        
        # 获取父坐标点
        parent_points = sampled_df[[x_col_name, y_col_name]].values
        
        # 为每个父坐标找到四边形边界上最近的点
        nearest_points = np.zeros((len(parent_points), 2))
        
        for i, parent_point in enumerate(parent_points):
            # 找到所有四边形边界上的最近点
            min_dist = float('inf')
            nearest_point = None
            
            for quad in self.quadrilateral_points:
                # 计算点到四边形边界的最近点
                boundary_point, dist = self._nearest_point_on_boundary(parent_point, quad)
                
                if dist < min_dist:
                    min_dist = dist
                    nearest_point = boundary_point
            
            if nearest_point is not None:
                nearest_points[i] = nearest_point
            else:
                # 如果没有找到最近点（理论上不应该发生），使用原坐标
                nearest_points[i] = parent_point
        
        # 创建结果DataFrame
        nearest_df = sampled_df.copy()
        nearest_df[x_col_name] = nearest_points[:, 0]
        nearest_df[y_col_name] = nearest_points[:, 1]
        
        # 更新结果
        result['sampled_df'] = nearest_df
        
        return result
    
    def _nearest_point_on_boundary(self, point, quad):
        """计算点到四边形边界的最近点
        
        Args:
            point: 要计算的点坐标 [x, y]
            quad: 四边形的角点坐标，形状为(n, 2)
            
        Returns:
            tuple: (最近点坐标, 距离)
        """
        min_dist = float('inf')
        nearest_point = None
        
        # 检查点是否在四边形内部
        if self._is_point_in_quadrilateral(point, quad):
            # 如果点在四边形内部，它就是自己的最近点
            return point, 0.0
        
        # 检查所有边
        n = len(quad)
        for i in range(n):
            j = (i + 1) % n
            edge_point, dist = self._nearest_point_on_line_segment(point, quad[i], quad[j])
            
            if dist < min_dist:
                min_dist = dist
                nearest_point = edge_point
        
        return nearest_point, min_dist
    
    def _nearest_point_on_line_segment(self, point, line_start, line_end):
        """计算点到线段的最近点
        
        Args:
            point: 要计算的点坐标 [x, y]
            line_start: 线段起点坐标 [x, y]
            line_end: 线段终点坐标 [x, y]
            
        Returns:
            tuple: (最近点坐标, 距离)
        """
        # 线段向量
        line_vec = line_end - line_start
        line_len_squared = np.sum(line_vec**2)
        
        if line_len_squared == 0:
            # 线段退化为点
            return line_start, np.linalg.norm(point - line_start)
        
        # 计算投影比例 t
        t = max(0, min(1, np.dot(point - line_start, line_vec) / line_len_squared))
        
        # 计算最近点
        projection = line_start + t * line_vec
        
        # 计算距离
        dist = np.linalg.norm(point - projection)
        
        return projection, dist
    
    def _is_point_in_quadrilateral(self, point, quad):
        """判断点是否在多边形内（包括边界）
        
        使用射线法（Ray Casting Algorithm）判断点是否在多边形内
        
        Args:
            point: 要判断的点坐标 [x, y]
            quad: 多边形的角点坐标，形状为(n, 2)
            
        Returns:
            bool: 如果点在多边形内（包括边界）返回True，否则返回False
        """
        x, y = point
        n = len(quad)
        inside = False
        
        # 检查点是否在任意边上
        for i in range(n):
            j = (i + 1) % n
            if self._is_point_on_line_segment(point, quad[i], quad[j]):
                return True
        
        # 射线法判断点是否在多边形内部
        for i in range(n):
            j = (i + 1) % n
            if ((quad[i][1] > y) != (quad[j][1] > y)) and \
               (x < (quad[j][0] - quad[i][0]) * (y - quad[i][1]) / (quad[j][1] - quad[i][1]) + quad[i][0]):
                inside = not inside
                
        return inside
    
    def _is_point_on_line_segment(self, point, line_start, line_end, epsilon=1e-9):
        """判断点是否在线段上
        
        Args:
            point: 要判断的点坐标 [x, y]
            line_start: 线段起点坐标 [x, y]
            line_end: 线段终点坐标 [x, y]
            epsilon: 浮点数比较的容差值
            
        Returns:
            bool: 如果点在线段上返回True，否则返回False
        """
        x, y = point
        x1, y1 = line_start
        x2, y2 = line_end
        
        # 检查点是否在线段的边界框内
        if not (min(x1, x2) - epsilon <= x <= max(x1, x2) + epsilon and 
                min(y1, y2) - epsilon <= y <= max(y1, y2) + epsilon):
            return False
        
        # 如果线段是垂直的
        if abs(x1 - x2) < epsilon:
            return abs(x - x1) < epsilon
        
        # 如果线段是水平的
        if abs(y1 - y2) < epsilon:
            return abs(y - y1) < epsilon
        
        # 检查点是否在线段上（使用叉积）
        cross_product = abs((y - y1) * (x2 - x1) - (x - x1) * (y2 - y1))
        return cross_product < epsilon
    
    def get_description(self):
        """返回策略描述"""
        return f"近邻 ({len(self.quadrilateral_points)}个Panel)"

    def get_params(self):
        """返回序列化参数"""
        # 将四边形点转换为列表格式，以便正确序列化
        quad_points_list = []
        for i, quad in enumerate(self.quadrilateral_points):
            quad_list = quad.tolist()
            
            # 获取panel名称
            panel_name = self.panel_names[i] if i < len(self.panel_names) else f"四边形 #{i+1}"
            
            # 添加到列表中
            quad_points_list.append({
                'points': quad_list,
                'panel_name': panel_name
            })
                
        return {
            "type": self.strategy_type,
            "quadrilateral_points": quad_points_list
        } 