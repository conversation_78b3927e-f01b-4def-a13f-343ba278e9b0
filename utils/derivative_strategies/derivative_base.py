class DerivativeStrategy:
    """派生策略基类，定义了派生点集计算的接口"""
    strategy_type = None  # 子类必须定义此类属性

    def calculate(self, parent_result):
        """根据父结果计算派生结果"""
        raise NotImplementedError()

    def get_description(self):
        """返回派生描述文本"""
        raise NotImplementedError()

    def get_params(self):
        """返回策略参数，用于序列化"""
        raise NotImplementedError()

    def get_type(self):
        """返回策略类型标识符"""
        return self.strategy_type
