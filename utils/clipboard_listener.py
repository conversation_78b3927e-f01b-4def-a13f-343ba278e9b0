from PySide6.QtCore import QObject, QTimer, Signal
from PySide6.QtWidgets import QApplication
from utils.parse_clipboard_data import parse_clipboard_data


class ClipboardListener(QObject):
    """剪贴板监听器基类"""
    
    # 定义信号
    data_processed = Signal(object, object, object, object, object)  # df, all_columns, x_col_name, y_col_name, shot_id_col_name
    status_changed = Signal(str, bool)  # message, is_error
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.is_listening = False
        self.last_clipboard_content = ""
        
        # 初始化剪贴板
        self.clipboard = QApplication.clipboard()
        
        # 添加消抖定时器
        self.debounce_timer = QTimer(self)
        self.debounce_timer.setSingleShot(True)
        self.debounce_timer.setInterval(200)
        self.debounce_timer.timeout.connect(self.process_clipboard_after_delay)
        
        # 连接剪贴板信号
        self.clipboard.dataChanged.connect(self.on_clipboard_changed)
    
    def start_listening(self):
        """开始监听剪贴板"""
        self.is_listening = True
        self.status_changed.emit("开始监听剪贴板...", False)
    
    def stop_listening(self):
        """停止监听剪贴板"""
        self.is_listening = False
        self.status_changed.emit("停止监听剪贴板", False)
    
    def on_clipboard_changed(self):
        """剪贴板内容变化时触发，启动消抖定时器"""
        if not self.is_listening:
            return
            
        self.status_changed.emit("检测到剪贴板更新...", False)
        self.debounce_timer.stop()
        self.debounce_timer.start()
    
    def process_clipboard_after_delay(self):
        """消抖延时后处理剪贴板内容"""
        if not self.is_listening:
            return
            
        try:
            mime_data = self.clipboard.mimeData()
            
            if mime_data.hasText():
                text = mime_data.text()
                if text and len(text.strip()) > 0:
                    self.last_clipboard_content = text
                    self.process_clipboard_data(text)
                else:
                    self.status_changed.emit("剪贴板文本内容为空", True)
            else:
                self.status_changed.emit("剪贴板不包含文本内容", True)
        except Exception as e:
            self.status_changed.emit(f"读取剪贴板失败: {str(e)}", True)
    
    def process_clipboard_data(self, text):
        """处理剪贴板数据，子类可以重写此方法以实现自定义处理逻辑"""
        try:
            df, all_columns, x_col_name, y_col_name, shot_id_col_name = parse_clipboard_data(text)
            if df is not None and df.shape[0] > 0 and x_col_name and y_col_name:
                self.data_processed.emit(df, all_columns, x_col_name, y_col_name, shot_id_col_name)
                self.status_changed.emit("坐标读取成功", False)
            else:
                self.status_changed.emit("剪贴板中未识别到坐标数据", True)
        except Exception as e:
            self.status_changed.emit(f"处理剪贴板数据时出错: {str(e)}", True)
    
    def cleanup(self):
        """清理资源"""
        self.stop_listening()
        self.clipboard.dataChanged.disconnect(self.on_clipboard_changed) 