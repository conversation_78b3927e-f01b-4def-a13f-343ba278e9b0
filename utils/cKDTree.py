from collections import namedtuple

import numpy as np

Result = namedtuple('Result', ['distance', 'index'])

class Node:
    def __init__(self, point, left=None, right=None):
        self.point = point
        self.left = left
        self.right = right


class cKDTree:
    def __init__(self, data):
        self.data = np.array(data)
        self.n = self.data.shape[0]
        self.root = self._build(np.arange(self.n), 0)

    def _build(self, indices, depth):
        if len(indices) == 0:
            return None

        k = self.data.shape[1]
        axis = depth % k

        indices = sorted(indices, key=lambda idx: self.data[idx, axis])
        median = len(indices) // 2

        return Node(
            indices[median],
            left=self._build(indices[:median], depth + 1),
            right=self._build(indices[median + 1:], depth + 1)
        )

    def query(self, x, k=1):
        x = np.asarray(x)
        if k > self.n:
            k = self.n

        def search(node, depth, heap):
            if node is None:
                return

            d = np.linalg.norm(self.data[node.point] - x)
            if len(heap) < k:
                heap.append(Result(d, node.point))
                heap.sort(key=lambda r: r.distance)
            elif d < heap[-1].distance:
                heap[-1] = Result(d, node.point)
                heap.sort(key=lambda r: r.distance)

            axis = depth % self.data.shape[1]
            diff = x[axis] - self.data[node.point, axis]

            close, far = (node.left, node.right) if diff <= 0 else (node.right, node.left)

            search(close, depth + 1, heap)
            if len(heap) < k or abs(diff) < heap[-1].distance:
                search(far, depth + 1, heap)

        heap = []
        search(self.root, 0, heap)

        distances = [r.distance for r in heap]
        indices = [r.index for r in heap]

        return np.array(distances), np.array(indices)