import numpy as np
import logging

logger = logging.getLogger(__name__)

def tp_sort(coords, shot_id, cancel_checker=None):
    # 检查取消请求
    if cancel_checker and cancel_checker.check_cancelled():
        return None

    # 检查输入数据的基本要求
    if shot_id is None or len(coords) != len(shot_id):
        raise ValueError(f"坐标数量({len(coords)})与Shot_ID数量不匹配")

    total_points = len(coords)
    if total_points not in [32, 48]:
        raise ValueError(f"已采样坐标数({total_points})必须是32或48")

    # 检查每个shot_id组是否都包含8个点
    unique_groups = np.unique(shot_id)
    for group in unique_groups:
        # 检查取消请求
        if cancel_checker and cancel_checker.check_cancelled():
            return None

        group_size = np.sum(shot_id == group)
        if group_size != 8:
            raise ValueError(f"Shot ID {group} 包含 {group_size} 个点，应该包含8个点")

    # 检查shot_id组的数量
    num_groups = len(unique_groups)
    if num_groups not in [4, 6]:
        raise ValueError(f"Shot数量({num_groups})必须是4或6")

    # 识别分布类型
    unique_groups = np.unique(shot_id)
    num_centroids = len(unique_groups)
    if num_centroids == 4:
        num_rows, num_cols = 2, 2
    elif num_centroids == 6:
        # 检查取消请求
        if cancel_checker and cancel_checker.check_cancelled():
            return None

        # 计算每组的重心
        centroids = []
        for group in unique_groups:
            indices = np.where(shot_id == group)[0]
            group_coords = coords[indices]
            centroid = np.mean(group_coords, axis=0)
            centroids.append(centroid)
        centroids = np.array(centroids)

        # 计算中心点（使用所有点的外接矩形中心）
        min_coords = np.min(centroids, axis=0)
        max_coords = np.max(centroids, axis=0)
        center = (min_coords + max_coords) / 2

        # 计算每个点到中心的距离
        distances = np.sqrt(np.sum((centroids - center) ** 2, axis=1))

        # 找出距离中心最远的4个点的索引（这些是角点）
        corner_indices = np.argsort(distances)[-4:]
        corner_points = centroids[corner_indices]

        # 找出剩余的2个点（腰点）
        middle_indices = np.setdiff1d(np.arange(6), corner_indices)
        middle_points = centroids[middle_indices]

        # 检查取消请求
        if cancel_checker and cancel_checker.check_cancelled():
            return None

        # 计算腰点到每个角点的平均距离
        distances_to_corners = []
        for middle_point in middle_points:
            dist = np.sqrt(np.sum((corner_points - middle_point) ** 2, axis=1))
            distances_to_corners.append(dist)
        distances_to_corners = np.array(distances_to_corners)

        # 对于每个腰点，找出最近的两个角点
        nearest_corners = []
        for dist in distances_to_corners:
            nearest_two_idx = np.argsort(dist)[:2]
            nearest_corners.append(corner_points[nearest_two_idx])

        # 对于每个腰点找出最近的两个角点后，直接判断方向
        # 计算两组最近角点之间的连线向量
        vectors = []
        for corners in nearest_corners:
            vector = corners[1] - corners[0]
            vectors.append(vector)

        # 计算向量的x和y分量的绝对值
        abs_x1, abs_y1 = abs(vectors[0][0]), abs(vectors[0][1])
        abs_x2, abs_y2 = abs(vectors[1][0]), abs(vectors[1][1])

        # 计算水平和垂直趋势的强度
        horizontal_strength = max(abs_x1 / max(abs_y1, 1e-6), abs_x2 / max(abs_y2, 1e-6))
        vertical_strength = max(abs_y1 / max(abs_x1, 1e-6), abs_y2 / max(abs_x2, 1e-6))

        # 判断排列方式
        if horizontal_strength > vertical_strength:
            num_rows, num_cols = 2, 3
        else:
            num_rows, num_cols = 3, 2

        logger.info(f"识别为{num_rows}行{num_cols}列排列")
    else:
        raise ValueError(f"不支持的组数：{num_centroids}")

    # 检查取消请求
    if cancel_checker and cancel_checker.check_cancelled():
        return None

    # 按Y坐标降序排列
    sorted_indices_y = np.argsort(-coords[:, 1])
    coords_sorted_y = coords[sorted_indices_y]
    shot_id_sorted_y = shot_id[sorted_indices_y]

    # 根据分布类型分组并排序
    total_points = len(coords_sorted_y)
    sorted_coords = []
    sorted_shot_ids = []
    current_index = 0
    group_number = 1

    # 设定分组大小
    if (num_rows == 2 and num_cols == 2) or (num_rows == 3 and num_cols == 2):
        group_sizes = [6, 4, 6]
    elif num_rows == 2 and num_cols == 3:
        group_sizes = [9, 6, 9]
    else:
        raise ValueError(f"未知的分布类型：{num_rows}行x{num_cols}列")

    # 分组并排序
    while current_index < total_points:
        # 检查取消请求
        if cancel_checker and cancel_checker.check_cancelled():
            return None

        group_size = group_sizes[(group_number - 1) % len(group_sizes)]
        end_index = min(current_index + group_size, total_points)
        group_coords = coords_sorted_y[current_index:end_index]
        group_shot_ids = shot_id_sorted_y[current_index:end_index]

        # 按X坐标排序（奇数组降序，偶数组升序）
        sort_order = -1 if group_number % 2 == 1 else 1
        sorted_indices_x = np.argsort(sort_order * group_coords[:, 0])

        group_coords_sorted = group_coords[sorted_indices_x]
        group_shot_ids_sorted = group_shot_ids[sorted_indices_x]

        sorted_coords.append(group_coords_sorted)
        sorted_shot_ids.append(group_shot_ids_sorted)

        current_index = end_index
        group_number += 1

    # 合并结果
    sorted_coords = np.vstack(sorted_coords)
    sorted_shot_ids = np.concatenate(sorted_shot_ids)

    return sorted_coords


def snake_sort(coords, head, dire, tolerance_factor, cancel_checker=None):
    # 检查取消请求
    if cancel_checker and cancel_checker.check_cancelled():
        return None

    coords = np.array(coords)

    # 1. 确定主轴和次轴
    if dire == 'X':
        main_axis = 0  # X轴
        secondary_axis = 1  # Y轴
    elif dire == 'Y':
        main_axis = 1  # Y轴
        secondary_axis = 0  # X轴
    else:
        raise ValueError("dire 必须是 'X' 或 'Y'")

    main_axis_name = 'X' if main_axis == 0 else 'Y'
    secondary_axis_name = 'X' if secondary_axis == 0 else 'Y'

    # 2. 确定起始方向
    head_dict = {
        'lt': {'X': 'min', 'Y': 'max'},  # 左上角
        'lb': {'X': 'min', 'Y': 'min'},  # 左下角
        'rt': {'X': 'max', 'Y': 'max'},  # 右上角
        'rb': {'X': 'max', 'Y': 'min'}  # 右下角
    }

    if head not in head_dict:
        raise ValueError("head 必须是 'lt', 'lb', 'rt', 'rb' 之一")

    # 检查取消请求
    if cancel_checker and cancel_checker.check_cancelled():
        return None

    # 获取起始位置在主轴和次轴上的极值
    head_main_pos = head_dict[head][main_axis_name]
    head_secondary_pos = head_dict[head][secondary_axis_name]

    # 确定主轴和次轴的初始遍历方向
    if head_main_pos == 'max':
        initial_main_dir = -1  # 递减
    elif head_main_pos == 'min':
        initial_main_dir = +1  # 递增
    else:
        raise ValueError("无效的主轴位置")

    if head_secondary_pos == 'max':
        secondary_dir = -1  # 递减
    elif head_secondary_pos == 'min':
        secondary_dir = +1  # 递增
    else:
        raise ValueError("无效的次轴位置")

    # 3. 按次轴对坐标排序
    sorted_indices = np.argsort(coords[:, secondary_axis] * secondary_dir)
    coords = coords[sorted_indices]

    # 计算次轴的平均间距
    secondary_vals = coords[:, secondary_axis]
    avg_spacing = np.mean(np.abs(np.diff(secondary_vals)))

    # 计算实际的 tolerance
    tolerance = avg_spacing * tolerance_factor

    # 检查取消请求
    if cancel_checker and cancel_checker.check_cancelled():
        return None

    diff_secondary = np.abs(np.diff(secondary_vals))
    # 找到组的边界
    cluster_boundaries = np.where(diff_secondary > tolerance)[0] + 1
    # 添加开始和结束索引
    cluster_indices = np.concatenate(([0], cluster_boundaries, [len(coords)]))

    sorted_coords = []
    current_main_dir = initial_main_dir

    # 4. 遍历每个组，按主轴排序，并反转方向
    for i in range(len(cluster_indices) - 1):
        # 检查取消请求
        if cancel_checker and cancel_checker.check_cancelled():
            return None

        start_idx = cluster_indices[i]
        end_idx = cluster_indices[i + 1]
        group_coords = coords[start_idx:end_idx]
        # 按主轴排序
        group_coords = group_coords[np.argsort(group_coords[:, main_axis] * current_main_dir)]
        sorted_coords.append(group_coords)
        # 反转主轴方向
        current_main_dir *= -1

    # 将排序后的组连接起来
    sorted_coords = np.concatenate(sorted_coords)

    return sorted_coords


def sort_coords(coords, sort_mode_list, shot_id=None, cancel_checker=None):
    # 检查取消请求
    if cancel_checker and cancel_checker.check_cancelled():
        return None

    if sort_mode_list['mode'] == '不排序':
        sorted_coords = coords
    elif sort_mode_list['mode'] == 'TP&OVL':
        sorted_coords = tp_sort(coords, shot_id, cancel_checker)
    else:  # sort_mode_list['mode'] == 'custom'
        sorted_coords = snake_sort(coords,
                                   sort_mode_list['head'],
                                   sort_mode_list['dire'],
                                   sort_mode_list['tolerance'],
                                   cancel_checker)
    return sorted_coords