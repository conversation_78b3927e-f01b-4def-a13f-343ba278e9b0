from PySide6.QtCore import Signal, Qt, QMimeData, QModelIndex
from PySide6.QtGui import QAction, QIcon, QKeyEvent, QDrag
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QListView, QDialog, QDialogButtonBox, QComboBox, QMenu, QMessageBox,
    QTabWidget, QTabBar, QInputDialog, QApplication
)

from views.custom_widgets import CustomLineEdit
from utils.derivative_strategies.derivative_factory import DerivativeStrategyFactory
import os
import sys
import logging

logger = logging.getLogger(__name__)


class DraggableTabBar(QTabBar):
    """可拖动的标签栏，使用QTabBar内置的拖拽功能"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setMovable(True)
        
        # 连接tabMoved信号到处理函数
        self.tabMoved.connect(self.on_tab_moved)
    
    def on_tab_moved(self, from_index, to_index):
        """处理标签移动事件"""
        # 由父窗口处理标签重排
        main_window = self.window()
        if isinstance(main_window, MainWindow):
            main_window.handle_tab_reorder(from_index, to_index)


class MainWindow(QMainWindow):
    save_all_signal = Signal()
    cache_signal = Signal()
    load_progress_signal = Signal()
    edit_names_signal = Signal(int)
    edit_sampling_signal = Signal(int)
    delete_result_signal = Signal(int)
    aboutToClose = Signal()
    
    # 重命名Sheet信号
    rename_sheet_signal = Signal(str, str)  # (old_name, new_name)
    reorder_sheets_signal = Signal(list)  # 新的排序列表

    # 通用派生点集信号
    create_dependency_signal = Signal(str, int)  # (strategy_type, parent_index, params)
    edit_dependency_signal = Signal(int, str)  # (index, strategy_type)

    def __init__(self):
        super().__init__()
        if hasattr(sys, '_MEIPASS'):
            self.main_path = sys._MEIPASS
        else:
            self.main_path = os.path.dirname(sys.modules['__main__'].__file__)
        self.results_model = None
        self.proxy_models = {}  # 存储每个sheet的代理模型
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle('坐标采样工具')

        # 获取屏幕尺寸
        screen = self.screen()
        screen_size = screen.availableGeometry()

        # 设置窗口初始大小为屏幕可用空间的50%
        width = int(screen_size.width() * 0.3)
        height = int(screen_size.height() * 0.5)
        self.resize(width, height)

        # 将窗口移动到屏幕中央
        self.move(
            (screen_size.width() - width) // 2,
            (screen_size.height() - height) // 2
        )

        self.setup_menu_bar()  # Add menu bar setup
        self.setup_central_widget()
        self.setup_window_icon()

    def setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        file_menu = menubar.addMenu('文件')
        file_menu.setToolTipsVisible(True)  # 启用菜单项的工具提示显示

        # 添加读取暂存文件的动作
        load_action = QAction(QIcon(os.path.join(self.main_path, "resources/打开.svg")), '读取进度', self)
        load_action.setToolTip("读取暂存的进度文件")
        load_action.triggered.connect(self.load_progress_signal.emit)
        file_menu.addAction(load_action)

        # 添加保存进度文件的动作
        save_action = QAction(QIcon(os.path.join(self.main_path, "resources/暂存.svg")), '暂存进度', self)
        save_action.setToolTip("将当前进度保存为工程文件")
        save_action.triggered.connect(self.cache_signal.emit)
        file_menu.addAction(save_action)

    def setup_central_widget(self):
        """设置中心窗口部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        self.setup_labels(layout)
        self.setup_tabs_widget(layout)
        self.setup_buttons(layout)

    def setup_labels(self, layout):
        """设置状态标签"""
        self.clipboard_status_label = QLabel("正在监听剪贴板...")
        self.clipboard_status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.clipboard_status_label)

    def setup_tabs_widget(self, layout):
        """设置标签页和结果列表"""
        self.tabs = QTabWidget()
        self.tabs.setTabBar(DraggableTabBar())  # 使用自定义的可拖动标签栏
        self.tabs.setTabsClosable(False)  # 不可通过关闭按钮关闭标签
        self.tabs.setMovable(True)  # 允许移动标签
        # 移除重复的tabMoved连接，现在由DraggableTabBar处理
        self.tabs.tabBar().setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.tabs.tabBar().customContextMenuRequested.connect(self.show_tab_context_menu)
        
        layout.addWidget(self.tabs)

    def setup_buttons(self, layout):
        """设置按钮组件"""
        button_layout = QHBoxLayout()

        # 设置按钮布局的间距
        button_layout.setSpacing(self.fontMetrics().height())

        self.save_all_button = QPushButton("保存至文件")
        self.save_all_button.setIcon(QIcon(os.path.join(self.main_path, "resources/保存.svg")))
        self.save_all_button.clicked.connect(self.save_all_signal.emit)
        self.save_all_button.setToolTip("将所有结果保存到Excel文件")

        button_layout.addWidget(self.save_all_button)
        layout.addLayout(button_layout)

    def setup_window_icon(self):
        """设置窗口图标"""
        self.setWindowIcon(QIcon(os.path.join(self.main_path, 'resources/logo.png')))

    def closeEvent(self, event):
        """重写关闭事件"""
        reply = QMessageBox.question(
            self,
            "确认退出",
            "确认退出？\n未保存的数据将会丢失。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No  # 默认选择"否"
        )

        if reply == QMessageBox.Yes:
            self.aboutToClose.emit()  # 发送关闭前信号
            event.accept()
        else:
            event.ignore()

    def keyPressEvent(self, event: QKeyEvent):
        """处理键盘事件"""
        if event.key() == Qt.Key_Delete:
            # 获取当前标签页中的列表视图
            current_tab_index = self.tabs.currentIndex()
            if current_tab_index >= 0:
                current_list_view = self.tabs.widget(current_tab_index).findChild(QListView)
                if current_list_view:
                    current_index = current_list_view.currentIndex()
                    if current_index.isValid():
                        # 获取源模型中的索引
                        model_index = self.map_to_source_index(current_index)
                        if model_index >= 0:
                            self.confirm_and_delete(model_index)
                            
        super().keyPressEvent(event)

    def confirm_and_delete(self, index):
        """确认并删除选中的结果"""
        # 检查是否为派生点集
        if self.results_model and self.results_model.is_dependent_result(index):
            result_name = self.results_model.data(self.results_model.index(index), Qt.DisplayRole)
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"{result_name} 是一个派生点集，删除后将无法恢复。\n确认删除？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
        else:
            result_name = self.results_model.data(self.results_model.index(index), Qt.DisplayRole)
            # 检查此结果是否有派生点集
            has_dependents = (self.results_model and
                              index < self.results_model.rowCount() and
                              self.results_model.results[index].get('dependent_ids', []))

            if has_dependents:
                reply = QMessageBox.question(
                    self,
                    "确认删除",
                    f"{result_name} 有派生点集，删除它将同时删除所有派生点集。\n确认删除？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
            else:
                reply = QMessageBox.question(
                    self,
                    "确认删除",
                    f"确认删除 {result_name} ？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

        if reply == QMessageBox.Yes:
            self.delete_result_signal.emit(index)

    def map_to_source_index(self, proxy_index):
        """将代理模型索引映射到源模型索引"""
        if not proxy_index.isValid():
            return -1
            
        # 获取当前标签页的代理模型
        current_tab_index = self.tabs.currentIndex()
        if current_tab_index < 0:
            return -1
            
        sheet_name = self.tabs.tabText(current_tab_index)
        if sheet_name not in self.proxy_models:
            return -1
            
        proxy_model = self.proxy_models[sheet_name]
        # 映射到源模型索引
        source_index = proxy_model.mapToSource(proxy_index)
        return source_index.row()

    def show_results_context_menu(self, position):
        # 获取当前列表视图
        list_view = self.sender()
        if not list_view:
            return
            
        # 获取点击位置的索引
        proxy_index = list_view.indexAt(position)
        if not proxy_index.isValid():
            return
            
        # 映射到源模型索引
        source_row = self.map_to_source_index(proxy_index)
        if source_row < 0:
            return
            
        # 创建上下文菜单
        context_menu = QMenu()

        # 检查是否为派生点集
        is_dependent = (self.results_model and
                    self.results_model.is_dependent_result(source_row))
        
        # 获取当前sheet中的项目数量
        current_sheet = self.tabs.tabText(self.tabs.currentIndex())
        proxy_model = self.proxy_models.get(current_sheet)
        total_items = proxy_model.rowCount() if proxy_model else 0
        
        # 添加上移动作
        move_up = QAction(QIcon(os.path.join(self.main_path, "resources/上移.svg")), "上移", self)
        move_up.triggered.connect(lambda: self.move_item(source_row, -1))
        move_up.setEnabled(proxy_index.row() > 0)  # 第一项不能上移
        context_menu.addAction(move_up)
        
        # 添加下移动作
        move_down = QAction(QIcon(os.path.join(self.main_path, "resources/下移.svg")), "下移", self)
        move_down.triggered.connect(lambda: self.move_item(source_row, 1))
        move_down.setEnabled(proxy_index.row() < total_items - 1)  # 最后一项不能下移
        context_menu.addAction(move_down)

        context_menu.addSeparator()

        if is_dependent:
            # 获取派生类型
            dependency_type = self.results_model.get_dependency_type(source_row)
            if dependency_type:
                # 添加编辑派生点集操作
                self.add_edit_dependency_action(context_menu, source_row, dependency_type)
                context_menu.addSeparator()
        else:
            # 为非派生点集添加"派生"子菜单
            self.add_derive_menu(context_menu, source_row)
            context_menu.addSeparator()

            # 为非派生点集添加编辑操作
            self.add_edit_action(context_menu, source_row)

        # 所有点集都可以重命名项目名
        rename_action = QAction(QIcon(os.path.join(self.main_path, "resources/重命名.svg")), "重命名", self)
        rename_action.triggered.connect(lambda: self.edit_names_signal.emit(source_row))
        context_menu.addAction(rename_action)

            # 所有点集都可以删除
        self.add_delete_action(context_menu, source_row)

        context_menu.exec_(list_view.mapToGlobal(position))

    def move_item(self, source_row, direction):
        """移动项目
        
        Args:
            source_row: 源模型中的行索引
            direction: 移动方向，-1表示上移，1表示下移
        """
        if not self.results_model:
            return
            
        # 获取当前sheet的代理模型
        current_tab_index = self.tabs.currentIndex()
        if current_tab_index < 0:
            return
            
        current_sheet = self.tabs.tabText(current_tab_index)
        proxy_model = self.proxy_models.get(current_sheet)
        if not proxy_model:
            return
            
        # 将源模型索引映射到代理模型中的行
        source_proxy_row = -1
        for i in range(proxy_model.rowCount()):
            proxy_index = proxy_model.index(i, 0)
            source_index = proxy_model.mapToSource(proxy_index)
            if source_index.row() == source_row:
                source_proxy_row = i
                break
                
        if source_proxy_row < 0:
            return
            
        # 计算目标在代理模型中的行
        target_proxy_row = source_proxy_row + direction
        if target_proxy_row < 0 or target_proxy_row >= proxy_model.rowCount():
            return
            
        # 获取目标行在源模型中的索引
        target_proxy_index = proxy_model.index(target_proxy_row, 0)
        target_source_index = proxy_model.mapToSource(target_proxy_index)
        target_row = target_source_index.row()
        
        # 现在我们有了在同一sheet中的source_row和target_row，调用移动方法
        # 计算方向（1表示下移，-1表示上移）
        real_direction = 1 if target_row > source_row else -1
        
        # 直接调用模型的移动方法
        self.results_model.move_item_in_sheet(source_row, real_direction)

    def show_tab_context_menu(self, position):
        """显示标签页的上下文菜单"""
        tab_bar = self.tabs.tabBar()
        tab_index = tab_bar.tabAt(position)
        
        if tab_index < 0:
            return
            
        sheet_name = self.tabs.tabText(tab_index)
        
        context_menu = QMenu(self)
        
        # 添加重命名Sheet操作
        rename_action = QAction(QIcon(os.path.join(self.main_path, "resources/重命名.svg")), "重命名标签页", self)
        rename_action.triggered.connect(lambda: self.rename_sheet_tab(sheet_name))
        context_menu.addAction(rename_action)
        
        context_menu.exec_(tab_bar.mapToGlobal(position))

    def rename_sheet_tab(self, old_sheet_name):
        """重命名Sheet标签页"""
        new_sheet_name, ok = QInputDialog.getText(
            self, 
            "重命名标签页", 
            "输入新的标签页名称:",
            text=old_sheet_name
        )
        
        if ok and new_sheet_name and new_sheet_name != old_sheet_name:
            # 发出重命名信号
            self.rename_sheet_signal.emit(old_sheet_name, new_sheet_name)

    def add_derive_menu(self, context_menu, parent_index):
        """添加"派生"菜单及其子菜单项

        Args:
            context_menu: 父菜单
            parent_index: 选中的点集索引
        """
        # 创建"派生"子菜单
        derive_menu = QMenu("派生", self)

        # 设置icon
        derive_menu.setIcon(QIcon(os.path.join(self.main_path, "resources/派生.svg")))
        # 动态添加所有已注册的派生策略
        for strategy_type in DerivativeStrategyFactory.get_all_strategy_types():
            # 获取菜单信息
            menu_text = DerivativeStrategyFactory.get_menu_text(strategy_type)
            icon_path = DerivativeStrategyFactory.get_icon_path(strategy_type)

            if menu_text and icon_path:
                action = QAction(QIcon(os.path.join(self.main_path, icon_path)), menu_text, self)
                action.triggered.connect(lambda checked=False, st=strategy_type, idx=parent_index:
                                         self.show_dependency_dialog(st, idx))
                derive_menu.addAction(action)

        context_menu.addMenu(derive_menu)

    def add_edit_dependency_action(self, context_menu, index, dependency_type):
        """添加编辑派生点集的菜单项

        Args:
            context_menu: 菜单对象
            index: 点集索引
            dependency_type: 派生类型
        """
        # 获取菜单信息
        menu_text = DerivativeStrategyFactory.get_menu_text(dependency_type)
        icon_path = DerivativeStrategyFactory.get_icon_path(dependency_type)

        if menu_text and icon_path:
            # 菜单文本转换为"编辑XX"格式
            edit_text = f"编辑{menu_text.replace('创建', '')}"
            action = QAction(QIcon(os.path.join(self.main_path, icon_path)), edit_text, self)
            action.triggered.connect(lambda: self.show_dependency_dialog(dependency_type, index, True))
            context_menu.addAction(action)

    def show_dependency_dialog(self, strategy_type, index, is_edit=False):
        """请求显示派生点集对话框

        Args:
            strategy_type: 策略类型
            index: 点集索引
            is_edit: 是否为编辑模式
        """
        # 发送信号给控制器，请求显示对话框
        if is_edit:
            self.edit_dependency_signal.emit(index, strategy_type)
        else:
            self.create_dependency_signal.emit(strategy_type, index)

    def add_delete_action(self, context_menu, index):
        delete_action = QAction(QIcon(os.path.join(self.main_path, "resources/删除.svg")), "删除", self)
        delete_action.triggered.connect(lambda: self.confirm_and_delete(index))
        context_menu.addAction(delete_action)

    def add_edit_action(self, context_menu, index):
        """添加上下文菜单中的编辑操作"""
        edit_action = QAction(QIcon(os.path.join(self.main_path, "resources/编辑采样.svg")), "编辑采样", self)
        edit_action.triggered.connect(lambda: self.edit_sampling_signal.emit(index))
        context_menu.addAction(edit_action)

    def on_item_double_clicked(self, proxy_index):
        """处理列表项双击事件"""
        # 将代理索引映射到源模型索引
        source_row = self.map_to_source_index(proxy_index)
        if source_row < 0:
            return
            
        # 检查是否为派生点集
        if self.results_model and self.results_model.is_dependent_result(source_row):
            # 获取父点集信息
            parent_id = self.results_model.results[source_row].get('parent_id')
            if parent_id is not None and 0 <= parent_id < self.results_model.rowCount():
                parent = self.results_model.results[parent_id]
                parent_name = parent['project_name']
                parent_sheet = parent['sheet_name']
                
                # 显示提示，包含父点集信息
                QMessageBox.information(
                    self,
                    "无法编辑派生点集",
                    f"派生点集不能直接编辑。\n如需修改，请编辑其源点集: {parent_sheet}-{parent_name}",
                    QMessageBox.Ok
                )
            else:
                # 显示通用提示
                QMessageBox.information(
                    self,
                    "无法编辑派生点集",
                    "派生点集不能直接编辑。\n如需修改，请编辑其源点集。",
                    QMessageBox.Ok
                )
        else:
            self.edit_sampling_signal.emit(source_row)

    def get_project_name_dialog(self, project_name=""):
        """获取项目名称的对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle("输入项目名称")
        layout = QVBoxLayout(dialog)

        project_input = self.create_combobox("project_name", project_name, "输入/选择项目名称",
                                            ['CD', 'TP', 'OVL', '膜厚 Testkey', '膜厚 AA区', '色度'])
        layout.addWidget(project_input)
        project_input.setToolTip("备注量测项目名称")

        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        if dialog.exec() == QDialog.Accepted:
            return project_input.currentText()
        return project_name

    def get_names_dialog(self, sheet_name="", project_name=""):
        """获取Sheet名和备注的对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle("输入Sheet名和备注")
        layout = QVBoxLayout(dialog)

        sheet_input = self.create_combobox("sheet_name", sheet_name,
                                           "输入/选择Sheet名", ["BM", "RGB", "OC", "PS"])
        layout.addWidget(sheet_input)
        sheet_input.setToolTip("将体现在最终输出的Excel")

        project_input = self.create_combobox("project_name", project_name, "输入/选择备注",
                                             ['CD', 'TP', 'OVL', '膜厚 Testkey', '膜厚 AA区', '色度'])
        layout.addWidget(project_input)
        project_input.setToolTip("备注量测项目名称")

        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        if dialog.exec() == QDialog.Accepted:
            return sheet_input.currentText(), project_input.currentText()
        return sheet_name, project_name

    def create_combobox(self, name_type, current_text, placeholder_text, default_items):
        """创建可编辑的组合框"""
        combobox = QComboBox()
        items = self.results_model.get_all_results_names(name_type,
                                                         default_items) if self.results_model else default_items
        combobox.addItems(items)
        combobox.setEditable(True)
        combobox.setLineEdit(CustomLineEdit(combobox))
        combobox.setInsertPolicy(QComboBox.InsertAtTop)
        combobox.setCurrentText(current_text)
        combobox.lineEdit().setPlaceholderText(placeholder_text)
        return combobox

    def set_status(self, status, error_flag=False):
        """设置状态栏文本"""
        self.clipboard_status_label.setText(status)
        self.clipboard_status_label.setStyleSheet("color: red;" if error_flag else "")

    def set_results_model(self, results_model):
        """设置结果模型并刷新视图"""
        self.results_model = results_model
        self.refresh_tabs()

    def refresh_tabs(self):
        """刷新标签页，根据结果模型重新创建标签和列表"""
        if not self.results_model:
            return
            
        # 清空所有标签页和代理模型
        self.tabs.clear()
        self.proxy_models.clear()
        
        # 获取按顺序排列的Sheet名称
        sheet_names = self.results_model.get_ordered_sheet_names()
        
        # 为每个Sheet创建标签页和列表视图
        for sheet_name in sheet_names:
            # 创建列表视图容器
            list_container = QWidget()
            list_layout = QVBoxLayout(list_container)
            list_layout.setContentsMargins(0, 0, 0, 0)
            
            # 创建列表视图
            list_view = QListView()
            
            # 创建并设置代理模型
            proxy_model = self.results_model.create_sheet_proxy_model(sheet_name)
            list_view.setModel(proxy_model)
            
            # 存储代理模型以供后续使用
            self.proxy_models[sheet_name] = proxy_model
            
            # 设置列表视图属性
            list_view.setSelectionMode(QListView.SingleSelection)  # 单选模式
            list_view.setSelectionBehavior(QListView.SelectItems)  # 选择整个项目
            
            # 设置视图属性以优化显示
            list_view.setSpacing(2)  # 设置项目间距
            list_view.setUniformItemSizes(True)  # 统一项目大小以优化性能
            
            list_view.doubleClicked.connect(self.on_item_double_clicked)
            list_view.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
            list_view.customContextMenuRequested.connect(self.show_results_context_menu)
            
            # 添加列表视图到容器
            list_layout.addWidget(list_view)
            
            # 添加标签页
            self.tabs.addTab(list_container, sheet_name)

    def handle_tab_reorder(self, from_index, to_index):
        """处理标签拖放重排序"""
        # 获取当前顺序
        tab_count = self.tabs.count()
        if not (0 <= from_index < tab_count) or not (0 <= to_index < tab_count):
            return
            
        # 不需要再次移动标签，QTabBar已经处理了可视化移动
        # self.tabs.tabBar().moveTab(from_index, to_index)  # 删除这一行
        
        # 获取重排序后的所有sheet名称
        sheet_names = []
        for i in range(self.tabs.count()):
            sheet_names.append(self.tabs.tabText(i))
        
        # 发送重排序信号到控制器，更新模型
        self.reorder_sheets_signal.emit(sheet_names)

    def handle_sheet_rename(self, old_name, new_name):
        """处理Sheet重命名"""
        # 找到对应的标签索引
        for i in range(self.tabs.count()):
            if self.tabs.tabText(i) == old_name:
                # 更新标签文本
                self.tabs.setTabText(i, new_name)
                
                # 更新代理模型
                if old_name in self.proxy_models:
                    # 创建新的代理模型
                    new_proxy_model = self.results_model.create_sheet_proxy_model(new_name)
                    
                    # 获取当前标签页的列表视图
                    list_view = self.tabs.widget(i).findChild(QListView)
                    if list_view:
                        list_view.setModel(new_proxy_model)
                    
                    # 更新代理模型字典
                    self.proxy_models[new_name] = new_proxy_model
                    del self.proxy_models[old_name]
                break

    def update_after_model_change(self, select_sheet=None):
        """模型数据变更后更新UI
        
        Args:
            select_sheet: 更新后要选择的sheet名称，如果为None则尝试保持当前选中的sheet
        """
        # 记录当前选中的sheet
        current_sheet = None
        current_tab_index = self.tabs.currentIndex()
        
        if current_tab_index >= 0 and not select_sheet:
            current_sheet = self.tabs.tabText(current_tab_index)
        
        # 刷新标签页
        self.refresh_tabs()
        
        # 选择指定的sheet或恢复之前选择的sheet
        target_sheet = select_sheet or current_sheet
        if target_sheet:
            for i in range(self.tabs.count()):
                if self.tabs.tabText(i) == target_sheet:
                    self.tabs.setCurrentIndex(i)
                    break

    def get_results_model(self):
        """获取结果模型"""
        return self.results_model

    def show_restore_dialog(self):
        """显示恢复进度的确认对话框"""
        reply = QMessageBox.question(
            self,
            "崩溃恢复",
            "检测到上次程序异常退出，是否恢复进度？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.No:
            # 二次确认
            confirm_reply = QMessageBox.question(
                self,
                "确认取消恢复",
                "这将永久丢失崩溃前进度。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            return confirm_reply == QMessageBox.No  # 如果用户点击"否"，则返回True表示要恢复

        return True  # 用户直接点击"是"，恢复数据
