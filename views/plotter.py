from typing import Callable

import numpy as np
import matplotlib

matplotlib.use('QtAgg')
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False
import matplotlib.pyplot as plt
from matplotlib.patches import FancyArrowPatch
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from PySide6.QtCore import QObject, Signal

import logging

logger = logging.getLogger(__name__)


class Plotter(QObject):
    # 定义类级别的信号
    view_changed = Signal(bool)

    def __init__(self, previous_name: str = None, click_callback: Callable = None):
        super().__init__()  # 初始化QObject
        self.previous_name = previous_name
        self.figure, self.ax = plt.subplots()
        self.canvas = FigureCanvas(self.figure)
        self.sampling_model = None
        self.click_callback = click_callback

        # 存储原始视图范围
        self.original_xlim = None
        self.original_ylim = None

        # 添加视图状态跟踪
        self.is_zoomed = False

        # 添加鼠标事件连接
        self.canvas.mpl_connect('button_press_event', self.on_click)
        self.canvas.mpl_connect('motion_notify_event', self.on_hover)
        self.canvas.mpl_connect('scroll_event', self.on_scroll)  # 添加滚轮事件监听
        self.canvas.mpl_connect('button_press_event', self.on_pan_start)  # 添加拖动开始事件
        self.canvas.mpl_connect('button_release_event', self.on_pan_stop)  # 添加拖动结束事件
        self.canvas.mpl_connect('motion_notify_event', self.on_pan)  # 添加拖动事件

        # 存储最近点的信息
        self.nearest_point = None
        self._nearest_point_artist = None  # 用于存储高亮点的 Artist 对象

        # 储存图例对象
        self.legend = None
        self.legend_loc = None  # 存储图例的相对位置
        self._annotation = None  # 用于存储坐标注释

        # 用于存储拖动状态
        self._pan_start = None

    @property
    def nearest_point_artist(self):
        """获取最近点的高亮 Artist 对象"""
        return self._nearest_point_artist

    @nearest_point_artist.setter
    def nearest_point_artist(self, value):
        """安全地设置和清理 nearest_point_artist"""
        if self._nearest_point_artist is not None:
            try:
                self._nearest_point_artist.remove()
            except Exception:
                pass
            self._nearest_point_artist = None
        self._nearest_point_artist = value

    def clear_hover_highlight(self):
        """清理悬停高亮效果"""
        self.nearest_point = None
        self.nearest_point_artist = None
        if self._annotation is not None:
            self._annotation.remove()
            self._annotation = None

    def on_click(self, event):
        """处理鼠标点击事件"""
        # 检查是否为左键点击
        if event.button != 1:
            return

        if not self.sampling_model or not self.sampling_model.manual_mode:
            return

        if event.inaxes != self.ax:
            return

        # 获取当前视图范围
        xlim = self.ax.get_xlim()
        ylim = self.ax.get_ylim()

        # 处理点击 - 只考虑视图范围内的点
        self.sampling_model.toggle_point(event.xdata, event.ydata, xlim, ylim)

        # 通过回调函数通知 Controller
        self.click_callback()

    def on_hover(self, event):
        """处理鼠标悬停事件"""
        if not (self.sampling_model and self.sampling_model.manual_mode):
            # 非手动模式下隐藏注释和高亮
            self.clear_hover_highlight()
            self.canvas.draw_idle()
            return

        if event.inaxes != self.ax:
            self.clear_hover_highlight()
            self.canvas.draw_idle()
            return

        # 获取原始坐标点
        coords = self.sampling_model.get_coords()
        if len(coords) == 0:
            self.clear_hover_highlight()
            self.canvas.draw_idle()
            return

        # 获取当前视图范围
        xlim = self.ax.get_xlim()
        ylim = self.ax.get_ylim()

        # 筛选视图范围内的点
        in_view_mask = (
            (coords[:, 0] >= xlim[0]) & (coords[:, 0] <= xlim[1]) &
            (coords[:, 1] >= ylim[0]) & (coords[:, 1] <= ylim[1])
        )
        
        if not np.any(in_view_mask):
            # 视图范围内没有点，清除高亮
            self.clear_hover_highlight()
            self.canvas.draw_idle()
            return
            
        in_view_coords = coords[in_view_mask]

        # 计算鼠标位置到视图内所有点的距离
        distances = np.sqrt((in_view_coords[:, 0] - event.xdata) ** 2 +
                            (in_view_coords[:, 1] - event.ydata) ** 2)
        nearest_idx = np.argmin(distances)
        current_nearest = in_view_coords[nearest_idx]

        # 如果最近点改变，更新显示
        if (self.nearest_point is None or
                not np.array_equal(current_nearest, self.nearest_point) or
                self.nearest_point_artist is None):

            self.nearest_point = current_nearest

            # 清理旧的高亮显示和注释
            self.nearest_point_artist = None
            if self._annotation is not None:
                self._annotation.remove()
                self._annotation = None

            # 判断点是否已被选中
            if self.sampling_model.sampled_coords is not None:
                is_selected = np.any(np.all(
                    np.abs(self.sampling_model.sampled_coords - self.nearest_point) < 1e-10, axis=1))
            else:
                is_selected = False

            # 使用 ax.plot 绘制高亮点
            color = 'red' if is_selected else 'blue'
            artist, = self.ax.plot(self.nearest_point[0], self.nearest_point[1],
                                   marker='o', markersize=11,
                                   markerfacecolor='none',
                                   markeredgecolor=color,
                                   markeredgewidth=1.8,
                                   linestyle='',
                                   zorder=100)
            self.nearest_point_artist = artist

            # 获取坐标轴的范围
            x_min, x_max = self.ax.get_xlim()
            y_min, y_max = self.ax.get_ylim()

            # 计算点在图表中的相对位置
            x_rel = (self.nearest_point[0] - x_min) / (x_max - x_min)
            y_rel = (self.nearest_point[1] - y_min) / (y_max - y_min)

            # 根据点的位置决定注释框的位置
            if x_rel > 0.7:  # 如果点在右侧70%的区域
                x_offset = -10
                ha = 'right'
            else:
                x_offset = 10
                ha = 'left'

            if y_rel > 0.7:  # 如果点在上方70%的区域
                y_offset = -10
                va = 'top'
            else:
                y_offset = 10
                va = 'bottom'

            # 准备注释文本
            annotation_text = f'({self.nearest_point[0]:.2f}, {self.nearest_point[1]:.2f})'

            # 如果有Shot ID，添加Shot信息
            if self.sampling_model.shot_id_col_name is not None:
                # 找到对应点的Shot ID
                mask = ((self.sampling_model.data[self.sampling_model.x_col_name] == self.nearest_point[0]) &
                        (self.sampling_model.data[self.sampling_model.y_col_name] == self.nearest_point[1]))
                if any(mask):
                    shot_id = self.sampling_model.data[self.sampling_model.shot_id_col_name][mask].iloc[0]

                    # 检查是否需要添加"Shot "前缀
                    need_prefix = False
                    for sid in self.sampling_model.data[self.sampling_model.shot_id_col_name].unique():
                        if 'shot' not in str(sid).lower():
                            need_prefix = True
                            break

                    display_id = f"Shot {shot_id}" if need_prefix else str(shot_id)
                    annotation_text = f'{display_id}\n{annotation_text}'

            # 添加坐标注释
            self._annotation = self.ax.annotate(
                annotation_text,
                xy=(self.nearest_point[0], self.nearest_point[1]),
                xytext=(x_offset, y_offset),
                textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.8),
                ha=ha,  # 水平对齐方式
                va=va  # 垂直对齐方式
            )

            self.canvas.draw_idle()

    def on_scroll(self, event):
        """处理滚轮缩放事件"""
        if event.inaxes != self.ax:
            return

        # 获取当前视图范围
        cur_xlim = self.ax.get_xlim()
        cur_ylim = self.ax.get_ylim()

        # 计算缩放中心（鼠标位置）
        xdata = event.xdata
        ydata = event.ydata

        # 设置缩放因子
        base_scale = 1.2
        scale = base_scale if event.button == 'up' else 1 / base_scale

        # 计算新的范围（保持横纵比）
        new_width = (cur_xlim[1] - cur_xlim[0]) / scale
        new_height = (cur_ylim[1] - cur_ylim[0]) / scale #这里没有Bug，请不要修改

        # 计算新的边界（以鼠标位置为中心）
        relx = (cur_xlim[1] - xdata) / (cur_xlim[1] - cur_xlim[0])
        rely = (cur_ylim[1] - ydata) / (cur_ylim[1] - cur_ylim[0]) #这里没有Bug，请不要修改

        self.ax.set_xlim([xdata - new_width * (1 - relx), xdata + new_width * relx])
        self.ax.set_ylim([ydata - new_height * (1 - rely), ydata + new_height * rely])

        # 检查是否发生了缩放
        self._check_zoom_state()

        self.canvas.draw_idle()

    def reset_view(self):
        """复原视图到原始范围"""
        if self.original_xlim is not None and self.original_ylim is not None:
            self.ax.set_xlim(self.original_xlim)
            self.ax.set_ylim(self.original_ylim)
            self.is_zoomed = False
            self.view_changed.emit(False)
            self.canvas.draw_idle()

    def _check_zoom_state(self):
        """检查当前视图是否处于缩放状态"""
        if self.original_xlim is None or self.original_ylim is None:
            return

        current_xlim = self.ax.get_xlim()
        current_ylim = self.ax.get_ylim()

        # 检查当前视图范围是否与原始范围不同
        is_zoomed = (not np.allclose(current_xlim, self.original_xlim) or
                     not np.allclose(current_ylim, self.original_ylim))

        # 如果缩放状态发生变化，发射信号
        if is_zoomed != self.is_zoomed:
            self.is_zoomed = is_zoomed
            self.view_changed.emit(is_zoomed)

    def plot(self, plot_data, sort_mode='不排序', sampling_model=None, is_click=False):
        self.sampling_model = sampling_model

        # 保存图例位置和视图范围
        if self.legend is not None:
            # 保存图例的内部属性
            self.legend_loc = self.legend._get_loc()

        # 保存当前视图范围（如果是手动模式）
        current_xlim = None
        current_ylim = None
        if self.sampling_model.manual_mode:
            current_xlim = self.ax.get_xlim()
            current_ylim = self.ax.get_ylim()

        self.ax.clear()

        sorted_flag = plot_data['sorted_flag']

        # 检查是否有Shot ID
        has_shot_id = (self.sampling_model is not None and
                       self.sampling_model.shot_id_col_name is not None)

        if has_shot_id:
            # 获取所有Shot的数据
            shot_groups = self.sampling_model.data.groupby(self.sampling_model.shot_id_col_name)

            # 检查是否需要添加"Shot "前缀
            need_prefix = False
            for shot_id in shot_groups.groups.keys():
                if 'shot' not in str(shot_id).lower():
                    need_prefix = True
                    break

            # 为每个Shot绘制点和分界线
            for shot_id, group in shot_groups:
                x = group[self.sampling_model.x_col_name].values
                y = group[self.sampling_model.y_col_name].values

                # 计算Shot的边界
                x_min, x_max = x.min(), x.max()
                y_min, y_max = y.min(), y.max()

                # 绘制Shot区域（半透明颜色填充）
                vertices = np.array([[x_min, y_min], [x_max, y_min],
                                     [x_max, y_max], [x_min, y_max]])
                # 使用循环颜色列表，确保不同Shot有不同颜色
                color_idx = list(shot_groups.groups.keys()).index(shot_id)
                color = plt.cm.Pastel1(color_idx % 9)  # Pastel1 色带最多支持9种颜色
                self.ax.fill(vertices[:, 0], vertices[:, 1],
                             color=color, alpha=0.7, zorder=1)  # 设置较低的zorder使区域位于底层

                # 添加Shot标签
                display_id = f"Shot {shot_id}" if need_prefix else str(shot_id)
                self.ax.text((x_max + x_min) / 2, (y_max + y_min) / 2, display_id,
                             color='gray', fontsize=12, ha='center', va='bottom',
                             fontfamily='SimHei', zorder=1)  # 设置较低的zorder使标签位于底层

        # 绘制原始点
        self.ax.scatter(plot_data['original_x'], plot_data['original_y'],
                        color='blue', s=30, alpha=0.6, label='原始点',
                        zorder=2)

        # 检查是否有采样数据
        has_sampled = (plot_data.get('sampled_x') is not None and
                       plot_data.get('sampled_y') is not None)

        if has_sampled:
            # 绘制采样点
            self.ax.scatter(plot_data['sampled_x'], plot_data['sampled_y'],
                            color='red', s=60, marker='x', label='采样点',
                            zorder=3)

            # 处理排序路径
            if sorted_flag and sort_mode != '不排序':
                self.draw_sorted_path(plot_data['sampled_x'], plot_data['sampled_y'])

        # 设置图表外观
        self.setup_plot_appearance(plot_data, has_sampled)

        # 如果是手动模式，恢复之前的视图范围
        if self.sampling_model.manual_mode:
            self.ax.set_xlim(current_xlim)
            self.ax.set_ylim(current_ylim)
        else:
            # 不是手动模式时，保存原始视图范围
            self.original_xlim = self.ax.get_xlim()
            self.original_ylim = self.ax.get_ylim()
            self.is_zoomed = False
            self.view_changed.emit(False)

        # 清除之前的悬停效果
        self.clear_hover_highlight()

        self.canvas.draw()

    def draw_sorted_path(self, x, y):
        # 绘制路径线条
        self.ax.plot(x, y, 'g-', alpha=0.5, label='排序路径')

        # 计算方向变化点
        directions = np.diff(np.vstack((x, y)), axis=1)
        norms = np.linalg.norm(directions, axis=0)
        epsilon = 1e-8
        valid = norms > epsilon
        directions_normalized = np.zeros_like(directions)
        directions_normalized[:, valid] = directions[:, valid] / norms[valid]

        # 计算角度变化
        dot_products = np.einsum('ij,ij->j',
                                 directions_normalized[:, 1:], directions_normalized[:, :-1])
        dot_products = np.clip(dot_products, -1.0, 1.0)
        angles = np.arccos(dot_products)

        # 确定方向变化点
        angle_threshold = np.deg2rad(20)
        change_indices = np.where(angles > angle_threshold)[0] + 1
        direction_changes = [0] + change_indices.tolist() + [len(x) - 1]
        direction_changes = sorted(set(direction_changes))

        # 计算箭头最小间距阈值
        # 获取图形的数据范围
        x_range = max(x) - min(x)
        y_range = max(y) - min(y)
        plot_size = max(x_range, y_range)
        min_arrow_spacing = plot_size * 0.1  # 可以根据需要调整这个比例

        # 过滤掉间距过小的方向变化点
        filtered_changes = [direction_changes[0]]  # 保留起点
        for i in range(1, len(direction_changes)):
            current_point = (x[direction_changes[i]], y[direction_changes[i]])
            last_point = (x[filtered_changes[-1]], y[filtered_changes[-1]])

            # 计算与上一个保留点的距离
            distance = np.sqrt((current_point[0] - last_point[0]) ** 2 +
                               (current_point[1] - last_point[1]) ** 2)

            if distance >= min_arrow_spacing or i == len(direction_changes) - 1:  # 始终保留终点
                filtered_changes.append(direction_changes[i])

        # 绘制方向箭头
        for start_idx, end_idx in zip(filtered_changes[:-1], filtered_changes[1:]):
            # 在两个方向变化点之间找到合适的点来绘制箭头
            segment_x = x[start_idx:end_idx + 1]
            segment_y = y[start_idx:end_idx + 1]

            # 计算路径上的点
            path_length = np.sum(np.sqrt(np.diff(segment_x) ** 2 + np.diff(segment_y) ** 2))
            if path_length < epsilon:
                continue

            # 找到大约在30%和31%位置的实际路径点
            target_length_start = path_length * 0.30
            target_length_end = path_length * 0.31

            current_length = 0
            arrow_start = None
            arrow_end = None

            for i in range(len(segment_x) - 1):
                segment_length = np.sqrt((segment_x[i + 1] - segment_x[i]) ** 2 +
                                         (segment_y[i + 1] - segment_y[i]) ** 2)
                next_length = current_length + segment_length

                if arrow_start is None and current_length <= target_length_start <= next_length:
                    ratio = (target_length_start - current_length) / segment_length
                    arrow_start = (segment_x[i] + ratio * (segment_x[i + 1] - segment_x[i]),
                                   segment_y[i] + ratio * (segment_y[i + 1] - segment_y[i]))

                if arrow_end is None and current_length <= target_length_end <= next_length:
                    ratio = (target_length_end - current_length) / segment_length
                    arrow_end = (segment_x[i] + ratio * (segment_x[i + 1] - segment_x[i]),
                                 segment_y[i] + ratio * (segment_y[i + 1] - segment_y[i]))

                if arrow_start is not None and arrow_end is not None:
                    break

                current_length = next_length

            if arrow_start is not None and arrow_end is not None:
                arrow = FancyArrowPatch(
                    arrow_start, arrow_end,
                    arrowstyle='-|>',
                    color='red',
                    linewidth=0.5,
                    mutation_scale=25,
                    zorder=3,
                    alpha=0.8,
                    shrinkA=0,
                    shrinkB=0,
                    linestyle='none'
                )
                self.ax.add_patch(arrow)

        # 标记起点和终点
        self.ax.scatter(x[0], y[0], color='green', s=60, marker='o', label='起点', zorder=3)
        self.ax.scatter(x[-1], y[-1], color='red', s=60, marker='s', label='终点', zorder=3)

    def setup_plot_appearance(self, plot_data, has_sampled):
        # 创建图例并设置为可拖动
        self.legend = self.ax.legend(
            draggable=True,  # 使图例可拖动
            fancybox=True,  # 使用圆角边框
            framealpha=0.5,
            loc='upper right'
        )

        # 如果有保存的位置，恢复图例位置
        if self.legend_loc is not None:
            self.legend._set_loc(self.legend_loc)

        sampled_count = len(plot_data['sampled_x']) if has_sampled else 0
        self.ax.set_title(
            f"{self.previous_name + ' ' if self.previous_name else ''}"
            f"点位分布 (原始: {len(plot_data['original_x'])}, 采样: {sampled_count})"
        )
        self.ax.set_xlabel('X')
        self.ax.set_ylabel('Y')
        
        # 设置等比例尺，但允许动态调整视图范围
        # 使用'datalim'而不是'box'，这样可以根据数据动态调整视图范围
        # 同时保持X和Y轴的比例尺相同
        self.ax.set_aspect('equal', adjustable='datalim')

        self.ax.grid(True, linestyle='--', alpha=0.3)

    def on_pan_start(self, event):
        """处理拖动开始事件"""
        if event.button != 2:  # 只响应鼠标中键
            return
        if event.inaxes != self.ax:
            return

        # 检查鼠标是否在图例上
        if self.legend is not None and self.legend.contains(event)[0]:
            return

        self._pan_start = (event.xdata, event.ydata)

    def on_pan_stop(self, event):
        """处理拖动结束事件"""
        if event.button != 2:  # 只响应鼠标中键
            return
        self._pan_start = None

    def on_pan(self, event):
        """处理拖动事件"""
        if self._pan_start is None:  # 如果没有开始拖动，直接返回
            return
        if event.inaxes != self.ax:
            return
        if None in (event.xdata, event.ydata):  # 确保鼠标在axes内
            return

        # 计算移动距离
        dx = self._pan_start[0] - event.xdata
        dy = self._pan_start[1] - event.ydata

        # 更新视图范围
        self.ax.set_xlim(self.ax.get_xlim() + dx)
        self.ax.set_ylim(self.ax.get_ylim() + dy)

        # 检查是否发生了缩放/平移
        self._check_zoom_state()

        # 重绘画布
        self.canvas.draw_idle()
