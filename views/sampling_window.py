from typing import Optional, Dict, Any, Callable
from dataclasses import dataclass
from enum import Enum

from PySide6.QtCore import Signal, Qt
from PySide6.QtGui import QIcon
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QRadioButton, QButtonGroup, QComboBox, QCheckBox
)

from contextlib import contextmanager

from views.plotter import Plotter
from views.custom_widgets import CustomLineEdit
import os
import sys
import logging

logger = logging.getLogger(__name__)


@dataclass
class UiConfig:
    """UI配置常量"""
    WINDOW_TITLE: str = '采样窗口'
    PLOT_STRETCH: int = 3
    BUTTON_MIN_WIDTH: int = 80
    STATUS_HEIGHT: int = 30


class SamplingMethod(Enum):
    """采样方法枚举"""
    EDGE = 'edge'
    GRID = 'grid'
    GRID_STRICT = 'grid_strict'
    CC = 'cc'


class SortMode(Enum):
    """排序模式枚举"""
    NONE = '不排序'
    TP_OVL = 'TP&OVL'
    CUSTOM = '自定义'


class SamplingWindow(QMainWindow):
    """
    采样窗口视图类

    负责显示采样相关的UI界面，处理用户输入，并通过信号通知控制器

    Signals:
        refresh: 刷新信号
        window_closed: 窗口关闭信号
        sampling_params_changed: 采样参数改变信号
        sort_params_changed: 排序参数改变信号
    """

    # 定义信号
    refresh = Signal()
    window_closed = Signal()
    sampling_params_changed = Signal()
    sort_params_changed = Signal()

    def __init__(self, has_shot_id: bool, previous_name: str = None, click_callback: Callable = None):
        """初始化采样窗口

        Args:
            has_shot_id: 是否包含Shot ID
        """
        super().__init__()
        if hasattr(sys, '_MEIPASS'):
            self.main_path = sys._MEIPASS
        else:
            self.main_path = os.path.dirname(sys.modules['__main__'].__file__)
        self.previous_name = previous_name
        self.has_shot_id = has_shot_id
        self.manual_mode = False
        self.click_callback = click_callback
        self._init_ui()
        self._setup_signals()

    def _init_ui(self):
        """初始化UI组件"""
        self._setup_window_properties()
        self._create_central_widget()
        self._create_layouts()
        self._create_components()

    def _setup_window_properties(self):
        """设置窗口属性"""
        self.setWindowTitle(UiConfig.WINDOW_TITLE)
        
        # 获取屏幕尺寸
        screen = self.screen()
        screen_size = screen.availableGeometry()
        
        # 设置窗口初始大小为屏幕可用空间的60%
        width = int(screen_size.width() * 0.4)
        height = int(screen_size.height() * 0.85)
        self.resize(width, height)
        
        # 将窗口移动到屏幕中央
        self.move(
            (screen_size.width() - width) // 2,
            (screen_size.height() - height) // 2
        )
        
        self._set_window_icon()

    def _set_window_icon(self):
        """设置窗口图标"""
        try:
            self.setWindowIcon(QIcon(os.path.join(self.main_path, 'resources/logo.png')))
        except Exception as e:
            logger.warning(f"无法加载窗口图标: {str(e)}")

    def _create_central_widget(self):
        """创建中央窗口部件"""
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        self.main_layout = QVBoxLayout(self.central_widget)

    def _create_layouts(self):
        """创建布局"""
        self.plot_layout = QVBoxLayout()
        self.control_layout = QVBoxLayout()
        self.params_layout = QVBoxLayout()
        self.buttons_layout = QHBoxLayout()

        self.main_layout.addLayout(self.plot_layout)
        self.main_layout.addLayout(self.control_layout)
        self.control_layout.addLayout(self.params_layout)
        self.control_layout.addLayout(self.buttons_layout)

    def _create_components(self):
        """创建UI组件"""
        self._create_plot()
        self._create_sampling_controls()
        self._create_sort_controls()
        self._create_buttons()
        self._create_status_bar()

    def _create_plot(self):
        """创建绘图组件"""
        self.plotter = Plotter(previous_name=self.previous_name, click_callback=self.click_callback)
        self.canvas = self.plotter.canvas
        
        self.plot_layout.addWidget(self.canvas, UiConfig.PLOT_STRETCH)
        self._setup_plot_context_menu()
        
        # 连接缩放事件
        try:
            if hasattr(self.plotter, 'view_changed'):
                self.plotter.view_changed.connect(self._on_view_changed)
            else:
                logger.warning("Plotter does not have view_changed signal")
        except Exception as e:
            logger.error(f"Failed to connect view_changed signal: {str(e)}")

    def _setup_plot_context_menu(self):
        """设置绘图区域上下文菜单"""
        self.canvas.setContextMenuPolicy(Qt.CustomContextMenu)

    def _create_sampling_controls(self):
        """创建采样控制组件"""
        # 采样单位选择
        self._create_sampling_unit_selector()

        # 采样方法选择
        self._create_sampling_method_selector()

        # 采样偏好选择
        self._create_sampling_preference_selector()

        # 采样点数输入
        self._create_points_input()

    def _create_sampling_unit_selector(self):
        """创建采样单位选择器"""
        unit_layout = QHBoxLayout()
        unit_label = QLabel("采样单位：")
        self.sampling_unit_combo = QComboBox()
        self.sampling_unit_combo.addItems(["全局", "每Shot"])
        self.sampling_unit_combo.setCurrentText("每Shot" if self.has_shot_id else "全局")
        self.sampling_unit_combo.setEnabled(self.has_shot_id)
        if not self.has_shot_id:
            self.sampling_unit_combo.setToolTip("当前坐标没有Shot ID，仅可全局采样")

        unit_layout.addWidget(unit_label)
        unit_layout.addWidget(self.sampling_unit_combo)
        self.params_layout.addLayout(unit_layout)

    def _create_sampling_method_selector(self):
        """创建采样方法选择器"""
        self.method_radio_group = QButtonGroup()
        method_layout = QHBoxLayout()
        method_label = QLabel("采样方式：")

        self.method_buttons = {
            SamplingMethod.EDGE: QRadioButton("四边"),
            SamplingMethod.GRID: QRadioButton("网格"),
            SamplingMethod.GRID_STRICT: QRadioButton("网格对齐"),
            SamplingMethod.CC: QRadioButton("四角+中心"),
        }

        tooltips = {
            SamplingMethod.EDGE: "仅在外围选取",
            SamplingMethod.GRID: "均匀选取",
            SamplingMethod.GRID_STRICT: "均匀选取，并保证横竖对齐\n*实验性功能",
            SamplingMethod.CC: "四角+非外围区域均匀选取",
        }

        method_layout.addWidget(method_label)
        for method, button in self.method_buttons.items():
            self.method_radio_group.addButton(button)
            method_layout.addWidget(button)
            button.setToolTip(tooltips[method])

        self.params_layout.addLayout(method_layout)

    def _create_sampling_preference_selector(self):
        """创建采样偏好选择器"""
        self.prefer_radio_group = QButtonGroup()
        prefer_layout = QHBoxLayout()
        prefer_label = QLabel("采样偏好：")

        self.prefer_buttons = {
            'default': QRadioButton("自动"),
            'X': QRadioButton("X优先"),
            'Y': QRadioButton("Y优先")
        }

        prefer_layout.addWidget(prefer_label)
        for button in self.prefer_buttons.values():
            self.prefer_radio_group.addButton(button)
            prefer_layout.addWidget(button)

        self.prefer_buttons['default'].setChecked(True)

        self.params_layout.addLayout(prefer_layout)

    def _create_points_input(self):
        """创建点数输入框"""
        input_layout = QHBoxLayout()
        input_layout.setSpacing(self.fontMetrics().height() // 2)
        
        # 创建勾选框
        self.separate_xy_checkbox = QCheckBox("指定行列数")

        # 创建输入框
        self.num_points_input = CustomLineEdit()
        self.num_points_input.setPlaceholderText("采样点数")
        self._setup_input_widget(self.num_points_input)
        
        self.sampling_size_y_input = CustomLineEdit()
        self.sampling_size_y_input.setPlaceholderText("行数")
        self._setup_input_widget(self.sampling_size_y_input)

        self.sampling_size_x_input = CustomLineEdit()
        self.sampling_size_x_input.setPlaceholderText("列数")
        self._setup_input_widget(self.sampling_size_x_input)
        
        # 添加计算标签
        self.calc_label = QLabel()
        self.calc_label.setToolTip("每采样单位的预计点数")

        # 初始状态设置
        self.sampling_size_x_input.hide()
        self.sampling_size_y_input.hide()
        self.calc_label.hide()
        
        # 布局
        input_layout.addWidget(self.separate_xy_checkbox)
        input_layout.addWidget(self.num_points_input)
        input_layout.addWidget(self.sampling_size_y_input)
        input_layout.addWidget(self.sampling_size_x_input)
        input_layout.addWidget(self.calc_label)
        self.params_layout.addLayout(input_layout)
        
        # 连接信号
        self.separate_xy_checkbox.toggled.connect(self._on_separate_xy_changed)
        self.sampling_size_x_input.textChanged.connect(self.update_calc_label)
        self.sampling_size_y_input.textChanged.connect(self.update_calc_label)
        for button in self.method_buttons.values():
            button.toggled.connect(self.update_calc_label)

    def _setup_input_widget(self, widget):
        """设置输入框的通用属性"""
        widget.setMinimumWidth(self.fontMetrics().horizontalAdvance("0") * 8)
        widget.setMinimumHeight(int(self.fontMetrics().height() * 1.5))

    def _create_sort_controls(self):
        """创建排序控制组件"""
        sort_layout = QHBoxLayout()

        # 排序模式
        sort_method_tooltip = {
            SortMode.TP_OVL: "适用于CF补正模板的排序方式",
            SortMode.CUSTOM: "蛇形排序"
        }

        sort_label = QLabel("排序方式：")
        self.sort_combo = QComboBox()
        self.sort_combo.addItems([mode.value for mode in SortMode])
        for mode, tooltip in sort_method_tooltip.items():
            index = self.sort_combo.findText(mode.value)
            if index != -1:
                self.sort_combo.setItemData(index, tooltip, Qt.ToolTipRole)

        # 排序参数
        self.sort_head_combo = QComboBox()
        self.sort_head_combo.addItems(["左上", "右上", "左下", "右下"])
        self.sort_head_combo.setToolTip("蛇形起始位置")

        self.sort_dire_combo = QComboBox()
        self.sort_dire_combo.addItems(["X", "Y"])
        self.sort_dire_combo.setToolTip("蛇形方向")

        self.sort_tolerance = CustomLineEdit()
        self.sort_tolerance.setPlaceholderText("分组误差")
        self.sort_tolerance.setToolTip("分行（或分列）的容差")

        # 初始禁用部分控件
        self.update_sort_controls_state(False)

        # 添加到布局
        sort_layout.addWidget(sort_label)
        sort_layout.addWidget(self.sort_combo, 3)
        sort_layout.addWidget(self.sort_head_combo, 3)
        sort_layout.addWidget(self.sort_dire_combo, 3)
        sort_layout.addWidget(self.sort_tolerance, 2)

        self.params_layout.addLayout(sort_layout)

    def _create_buttons(self):
        """创建按钮组件"""
        # 手动模式按钮
        self.manual_mode_btn = QPushButton("进入手动选点")
        self.manual_mode_btn.setIcon(QIcon(os.path.join(self.main_path, 'resources/手动.svg')))
        self.manual_mode_btn.setCheckable(True)
        self.manual_mode_btn.setMinimumWidth(UiConfig.BUTTON_MIN_WIDTH)

        # 重置视图按钮
        self.reset_view_btn = QPushButton("重置视图")
        self.reset_view_btn.setIcon(QIcon(os.path.join(self.main_path, 'resources/重置.svg')))
        self.reset_view_btn.setMinimumWidth(UiConfig.BUTTON_MIN_WIDTH)
        self.reset_view_btn.setEnabled(False)  # 初始状态禁用
        self.reset_view_btn.setToolTip("重置缩放")

        # 确认按钮
        self.confirm_button = QPushButton("确认")
        self.confirm_button.setIcon(QIcon(os.path.join(self.main_path, 'resources/确认.svg')))
        self.confirm_button.setEnabled(False)
        self.confirm_button.setMinimumWidth(UiConfig.BUTTON_MIN_WIDTH)

        # 添加到布局
        self.buttons_layout.addWidget(self.manual_mode_btn)
        self.buttons_layout.addWidget(self.reset_view_btn)
        self.buttons_layout.addWidget(self.confirm_button)

    def _create_status_bar(self):
        """创建状态栏"""
        self.status_label = QLabel()
        self.status_label.setFixedHeight(UiConfig.STATUS_HEIGHT)
        self.status_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.control_layout.addWidget(self.status_label)

    @contextmanager
    def _block_signals(self):
        """临时阻断信号的上下文管理器"""
        blocked_widgets = [
            self.sampling_unit_combo,
            self.num_points_input,
            self.method_radio_group,
            self.prefer_radio_group
        ]
        # 保存当前状态并阻断信号
        original_states = [widget.signalsBlocked() for widget in blocked_widgets]
        for widget in blocked_widgets:
            widget.blockSignals(True)
        try:
            yield
        finally:
            # 恢复原始状态
            for widget, state in zip(blocked_widgets, original_states):
                widget.blockSignals(state)

    @contextmanager
    def batch_update(self):
        """用于批量更新参数时阻断信号的上下文管理器"""
        blocked_widgets = [
            # 采样参数控件
            self.sampling_unit_combo,
            self.method_radio_group,
            self.prefer_radio_group,
            self.separate_xy_checkbox,
            self.num_points_input,
            self.sampling_size_x_input,
            self.sampling_size_y_input,
            # 排序参数控件
            self.sort_combo,
            self.sort_head_combo,
            self.sort_dire_combo,
            self.sort_tolerance
        ]
        # 保存当前状态并阻断信号
        original_states = [widget.signalsBlocked() for widget in blocked_widgets]
        for widget in blocked_widgets:
            widget.blockSignals(True)
        try:
            yield
        finally:
            # 恢复原始状态
            for widget, state in zip(blocked_widgets, original_states):
                widget.blockSignals(state)

    @contextmanager
    def _block_sampling_signals(self):
        """临时阻断采样相关的信号"""
        # 列出需要阻断的信号源
        signal_sources = [
            self.sampling_unit_combo,
            self.method_radio_group,
            self.prefer_radio_group,
            self.separate_xy_checkbox,
            self.num_points_input,
            self.sampling_size_x_input,
            self.sampling_size_y_input,
            self.sort_combo,
            self.sort_head_combo,
            self.sort_dire_combo,
            self.sort_tolerance
        ]
        for widget in signal_sources:
            widget.blockSignals(True)
        try:
            yield
        finally:
            for widget in signal_sources:
                widget.blockSignals(False)

    def restore_params(self, params: Dict[str, Any]) -> bool:
        """批量恢复参数设置

        Args:
            params: 包含所有参数的字典
        """
        if not params:
            return False

        try:
            with self.batch_update():
                # 恢复采样参数（包括分离XY和相关输入）
                if 'sampling_numerical' in params:
                    self.set_sampling_params(params['sampling_numerical'])
                else:
                    # 兼容旧格式
                    self.separate_xy_checkbox.setChecked(False)
                    if 'num_points' in params:
                        self.num_points_input.setText(str(params['num_points']))
                
                # 恢复其他基本参数
                self.set_sampling_method(params.get('method', 'edge'))
                self.set_sampling_unit(params.get('sampling_unit', '每Shot'))
                self.set_sampling_prefer(params.get('prefer', None))
                
                # 恢复排序参数
                sort_mode_list = params.get('sort_mode_list', {})
                self.set_sort_mode(sort_mode_list)
                
                # 设置排序标志（用于图表显示）
                if sort_mode_list and sort_mode_list.get('mode') not in [None, '不排序']:
                    self.sorted_flag = True
                else:
                    self.sorted_flag = False
                
                # 启用确认按钮
                self.enable_confirm(True)
            
            return True
        except Exception as e:
            logger.error(f"恢复参数错误: {str(e)}")
            return False

    def _setup_signals(self):
        """设置信号连接"""
        # 采样参数变化信号
        self._connect_sampling_signals()

        # 排序参数变化信号
        self._connect_sorting_signals()

        # 其他信号
        self._connect_misc_signals()

        # 确保所有信号都有对应的处理方法
        self.refresh.connect(self._on_refresh_requested)

    def _on_refresh_requested(self):
        """处理刷新请求"""
        logger.debug("刷新请求已触发")

    def _connect_sampling_signals(self):
        """连接采样相关信号"""
        self.sampling_unit_combo.currentTextChanged.connect(self._on_sampling_params_changed)
        self.num_points_input.textChanged.connect(self._on_sampling_params_changed)
        self.sampling_size_x_input.textChanged.connect(self._on_sampling_params_changed)
        self.sampling_size_y_input.textChanged.connect(self._on_sampling_params_changed)
        self.method_radio_group.buttonClicked.connect(self._on_sampling_params_changed)
        self.prefer_radio_group.buttonClicked.connect(self._on_sampling_params_changed)
        self.separate_xy_checkbox.toggled.connect(self._on_sampling_params_changed)

    def _connect_sorting_signals(self):
        """连接排序相关信号"""
        # 移除原有的连接方式，改为新的信号处理结构
        self.sort_combo.currentTextChanged.connect(self._on_sort_mode_changed)

        # 自定义排序参数变化的信号连接
        self.sort_head_combo.currentTextChanged.connect(self._on_custom_sort_params_changed)
        self.sort_dire_combo.currentTextChanged.connect(self._on_custom_sort_params_changed)
        self.sort_tolerance.textChanged.connect(self._on_custom_sort_params_changed)

    def _connect_misc_signals(self):
        """连接其他信号"""
        self.manual_mode_btn.clicked.connect(self._on_manual_mode_changed)
        self.reset_view_btn.clicked.connect(self._on_reset_view)

    # 信号处理方法
    def _on_sampling_params_changed(self):
        """采样参数变化处理"""
        self.sampling_params_changed.emit()

    def update_sort_controls_state(self, enable: bool):
        """更新排序控件状态"""
        self.sort_head_combo.setEnabled(enable)
        self.sort_dire_combo.setEnabled(enable)
        self.sort_tolerance.setEnabled(enable)

    def _on_sort_mode_changed(self, mode: str):
        """排序模式变更时的事件处理 - 仅发送信号
        
        此方法已被废弃，逻辑已移至controller层。
        保留此方法仅为了向后兼容，实际处理已移至SamplingController.on_sort_mode_changed
        
        Args:
            mode: 排序模式
        """
        self.sort_params_changed.emit()

    def _on_custom_sort_params_changed(self):
        """处理自定义排序参数变化"""
        # 仅在自定义模式下触发排序
        if self.sort_combo.currentText() == SortMode.CUSTOM.value:
            self.sort_params_changed.emit()

    def _on_manual_mode_changed(self, checked: bool):
        """手动模式变化处理"""
        self.manual_mode = checked
        self.manual_mode_btn.setText("退出手动选点" if checked else "进入手动选点")

    def _on_reset_view(self):
        """处理重置视图按钮点击事件"""
        self.plotter.reset_view()
        self.reset_view_btn.setEnabled(False)  # 重置后禁用按钮

    def _on_view_changed(self, is_zoomed: bool):
        """处理视图缩放状态变化

        Args:
            is_zoomed: 是否处于缩放状态
        """
        self.reset_view_btn.setEnabled(is_zoomed)

    # 公共接口方法
    def update_plot(self, plot_data: Dict[str, Any], sampling_model: Any, is_click: bool):
        """更新图表显示

        Args:
            plot_data: 绘图数据
            sampling_model: 采样模型
            is_click: 是否为点击事件
        """
        logger.info("更新图表显示")
        try:
            self.plotter.plot(plot_data, self.get_sort_mode(), sampling_model, is_click)
        except Exception as e:
            logger.error(f"更新图表失败: {str(e)}")
            self.set_status(f"更新失败: {str(e)}", True)
            self.enable_confirm(False)  # 禁用确认按钮

    def get_sort_mode(self) -> str:
        """获取当前排序模式"""
        return self.sort_combo.currentText()

    def get_sort_mode_list(self) -> Dict[str, Any]:
        """
        获取当前排序模式的参数列表

        Returns:
            Dict[str, Any]: 包含排序模式参数的字典
        """
        sort_mode_list = {
            'mode': None,
            'head': None,
            'dire': None,
            'tolerance': ''
        }

        current_mode = self.sort_combo.currentText()

        if current_mode == SortMode.NONE.value:
            sort_mode_list['mode'] = SortMode.NONE.value
        elif current_mode == SortMode.TP_OVL.value:
            sort_mode_list['mode'] = SortMode.TP_OVL.value
        elif current_mode == SortMode.CUSTOM.value:
            sort_mode_list.update({
                'mode': SortMode.CUSTOM.value,
                'head': self._get_sort_head_value(),
                'dire': self.sort_dire_combo.currentText(),
                'tolerance': self._get_sort_tolerance()
            })

        return sort_mode_list

    def _get_sort_head_value(self) -> str:
        """获取排序起点值"""
        head_mapping = {
            '左上': 'lt', '左下': 'lb',
            '右上': 'rt', '右下': 'rb'
        }
        return head_mapping.get(self.sort_head_combo.currentText(), 'lt')

    def _get_sort_tolerance(self) -> float:
        """获取排序容差值"""
        try:
            return float(self.sort_tolerance.text()) if self.sort_tolerance.text() else 0
        except ValueError:
            return 0

    def get_num_points(self) -> Optional[int]:
        """获取采样点数"""
        try:
            num_points = int(float(self.num_points_input.text().strip()))
            return num_points if num_points > 0 else None
        except (ValueError, TypeError):
            return None

    def get_sampling_unit(self) -> str:
        """获取采样单位"""
        return self.sampling_unit_combo.currentText()

    def get_sampling_method(self) -> str:
        """获取采样方法"""
        for method, button in self.method_buttons.items():
            if button.isChecked():
                return method.value
        return SamplingMethod.EDGE.value

    def get_sampling_prefer(self) -> Optional[str]:
        """获取采样偏好"""
        if self.prefer_buttons['default'].isChecked():
            return None
        return 'X' if self.prefer_buttons['X'].isChecked() else 'Y'

    def get_sampling_numerical(self) -> Dict[str, Any]:
        """获取采样参数
        
        Returns:
            Dict[str, Any]: 包含采样参数的字典，包括：
                - use_sampling_size: 是否使用采样尺寸模式
                - num_points: 采样点数（点数模式时有效）
                - sampling_size_x: X方向采样尺寸（采样尺寸模式时有效）
                - sampling_size_y: Y方向采样尺寸（采样尺寸模式时有效）
        """
        params = {
            'use_sampling_size': self.separate_xy_checkbox.isChecked()
        }
        
        if params['use_sampling_size']:
            try:
                params['sampling_size_x'] = int(self.sampling_size_x_input.text())
                params['sampling_size_y'] = int(self.sampling_size_y_input.text())
                if params['sampling_size_x'] <= 0 or params['sampling_size_y'] <= 0:
                    params['sampling_size_x'] = None
                    params['sampling_size_y'] = None
            except (ValueError, TypeError):
                params['sampling_size_x'] = None
                params['sampling_size_y'] = None
        else:
            try:
                params['num_points'] = int(float(self.num_points_input.text().strip()))
                if params['num_points'] <= 0:
                    params['num_points'] = None
            except (ValueError, TypeError):
                params['num_points'] = None
                
        return params

    def update_input_visibility(self, use_sampling_size: bool):
        """更新输入框的显隐状态和清空相应的输入
        
        Args:
            use_sampling_size: 是否使用采样尺寸模式
        """
        if use_sampling_size:
            self.num_points_input.hide()
            self.sampling_size_x_input.show()
            self.sampling_size_y_input.show()
            self.calc_label.show()
            self.num_points_input.clear()  # 清空点数输入
            self.update_calc_label()  # 更新计算公式标签
        else:
            self.num_points_input.show()
            self.sampling_size_x_input.hide()
            self.sampling_size_y_input.hide()
            self.calc_label.hide()
            self.sampling_size_x_input.clear()  # 清空XY输入
            self.sampling_size_y_input.clear()

    def update_calc_label(self):
        """更新计算标签显示"""
        try:
            # 获取X和Y的值
            try:
                x = int(self.sampling_size_x_input.text() or '0')
                y = int(self.sampling_size_y_input.text() or '0')
            except ValueError:
                self.calc_label.setText("")
                return

            # 根据不同的采样方法显示不同的计算公式
            if (self.method_buttons[SamplingMethod.GRID].isChecked()
                    or self.method_buttons[SamplingMethod.GRID_STRICT].isChecked()):
                result = x * y
                self.calc_label.setText(f"{y}(行) × {x}(列) = {result}")
            elif self.method_buttons[SamplingMethod.EDGE].isChecked():
                result = (x + y) * 2 - 4
                self.calc_label.setText(f"( {y}(行) + {x}(列) ) × 2 - 4 = {result}")
            elif self.method_buttons[SamplingMethod.CC].isChecked():
                result = x * y + 4
                self.calc_label.setText(f"{y}(行) × {x}(列) + 4 = {result}")
        except Exception as e:
            logger.error(f"更新计算标签错误: {str(e)}")
            self.calc_label.setText("")

    def set_sampling_params(self, params: Dict[str, Any]):
        """设置采样参数
        
        Args:
            params: 包含采样参数的字典
        """
        with self._block_sampling_signals():
            use_sampling_size = params.get('use_sampling_size', False)
            self.separate_xy_checkbox.setChecked(use_sampling_size)
            
            # 更新显隐状态
            self.update_input_visibility(use_sampling_size)
            
            # 设置输入值
            if use_sampling_size:
                if 'sampling_size_x' in params:
                    self.sampling_size_x_input.setText(str(params['sampling_size_x']))
                if 'sampling_size_y' in params:
                    self.sampling_size_y_input.setText(str(params['sampling_size_y']))
            else:
                if 'num_points' in params:
                    self.num_points_input.setText(str(params['num_points']))

    # 设置方法
    def set_num_points(self, value: str):
        """设置采样点数"""
        self.num_points_input.setText(str(value))

    def set_sampling_method(self, method: str):
        """设置采样方法"""
        for m, button in self.method_buttons.items():
            if m.value == method:
                button.setChecked(True)
                break

    def set_sampling_unit(self, unit: str):
        """设置采样单位"""
        self.sampling_unit_combo.setCurrentText(unit)

    def set_sampling_prefer(self, prefer: Optional[str]):
        """设置采样偏好"""
        if prefer is None:
            self.prefer_buttons['default'].setChecked(True)
        elif prefer == 'X':
            self.prefer_buttons['X'].setChecked(True)
        else:
            self.prefer_buttons['Y'].setChecked(True)

    def set_sort_mode(self, sort_mode_list: Optional[Dict[str, Any]]):
        """设置排序模式"""
        with self._block_sampling_signals():  # 阻断信号以防止多次触发
            if not sort_mode_list:
                self.sort_combo.setCurrentText(SortMode.NONE.value)
                self.update_sort_controls_state(False)
                return

            mode = sort_mode_list.get('mode')
            if mode in [m.value for m in SortMode]:
                self.sort_combo.setCurrentText(mode)
                # 更新控件状态
                enable_custom = mode == SortMode.CUSTOM.value
                self.update_sort_controls_state(enable_custom)
                if mode == SortMode.CUSTOM.value:
                    self._set_custom_sort_params(sort_mode_list)

    def _set_custom_sort_params(self, params: Dict[str, Any]):
        """设置自定义排序参数"""
        head_mapping = {
            'lt': '左上', 'lb': '左下',
            'rt': '右上', 'rb': '右下'
        }
        if params.get('head') in head_mapping:
            self.sort_head_combo.setCurrentText(head_mapping[params['head']])
        if params.get('dire'):
            self.sort_dire_combo.setCurrentText(params['dire'])
        if params.get('tolerance') is not None:
            self.sort_tolerance.setText(str(params['tolerance']))

    def set_status(self, message: str, error: bool = False):
        """设置状态栏信息
        
        Args:
            message: 状态消息
            error: 是否是错误消息
        """
        # 处理消息展示
        self.status_label.setText(message)
        self.status_label.setStyleSheet("color: #f44336" if error else "")

    def set_edge_distribution(self, edge_count: int, total_count: int):
        """设置边缘分布信息"""
        self.method_buttons[SamplingMethod.EDGE].setChecked(True)
        self.set_status(f"识别到可能为四边分布 ({edge_count}/{total_count})")

    def set_grid_distribution(self, grid_count: int, total_count: int):
        """设置网格分布信息"""
        if not any(btn.isChecked() for btn in [
            self.method_buttons[SamplingMethod.GRID],
            self.method_buttons[SamplingMethod.CC]
        ]):
            self.method_buttons[SamplingMethod.GRID].setChecked(True)
        self.set_status(f"识别到可能为网格分布 ({grid_count}/{total_count})")

    def enable_confirm(self, enabled: bool):
        """设置确认按钮状态"""
        self.confirm_button.setEnabled(enabled)

    def _on_separate_xy_changed(self):
        """处理分离XY选项变化"""
        with self._block_sampling_signals():
            use_sampling_size = self.separate_xy_checkbox.isChecked()
            self.update_input_visibility(use_sampling_size)
