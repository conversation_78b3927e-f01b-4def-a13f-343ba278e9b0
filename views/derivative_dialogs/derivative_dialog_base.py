from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QLabel, 
    QDialogButtonBox, QGroupBox
)


class DerivativeDialogBase(QDialog):
    """派生点集对话框的基类"""
    
    def __init__(self, parent=None, title="设置参数"):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setMinimumWidth(300)
        self.init_ui()
        
    def init_ui(self):
        """初始化基础UI组件"""
        layout = QVBoxLayout(self)
        
        # 添加说明文本
        self.desc_label = QLabel()
        self.desc_label.setWordWrap(True)
        layout.addWidget(self.desc_label)
        
        # 创建分组框
        self.group_box = QGroupBox("参数设置")
        self.group_layout = QVBoxLayout(self.group_box)
        layout.addWidget(self.group_box)
        
        # 添加按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        # 调用子类的具体UI初始化
        self.init_specific_ui()
    
    def init_specific_ui(self):
        """子类需要重写此方法来实现具体的UI初始化"""
        raise NotImplementedError("子类必须实现init_specific_ui方法")
    
    def set_description(self, text):
        """设置对话框的描述文本"""
        self.desc_label.setText(text)
    
    def get_params(self):
        """获取对话框的参数字典，子类需要重写此方法"""
        raise NotImplementedError("子类必须实现get_params方法") 