from PySide6.QtWidgets import QFormLayout, QDoubleSpinBox
from views.derivative_dialogs.derivative_dialog_base import DerivativeDialogBase


class DialogRotation(DerivativeDialogBase):
    """旋转变换设置对话框"""
    
    def __init__(self, parent=None, angle_degrees=0):
        self.initial_angle = angle_degrees
        super().__init__(parent, "设置旋转角度")
        
    def init_specific_ui(self):
        """初始化特定的UI组件"""
        # 设置描述文本
        self.set_description(
            "设置点集的旋转角度。\n"
            "所有点将以原点为中心进行旋转。"
        )
        
        # 创建表单布局
        form_layout = QFormLayout()
        self.group_layout.addLayout(form_layout)
        
        # 创建角度输入框
        self.angle_spinbox = QDoubleSpinBox()
        self.angle_spinbox.setRange(-360, 360)  # 角度范围
        self.angle_spinbox.setDecimals(2)
        self.angle_spinbox.setSingleStep(1)  # 步长
        self.angle_spinbox.setValue(self.initial_angle)
        self.angle_spinbox.setSuffix("°")
        self.angle_spinbox.setToolTip("旋转角度，正值为逆时针，负值为顺时针")
        form_layout.addRow("旋转角度:", self.angle_spinbox)
    
    def get_params(self):
        """获取对话框的参数字典"""
        return {
            'angle_degrees': self.angle_spinbox.value()
        } 