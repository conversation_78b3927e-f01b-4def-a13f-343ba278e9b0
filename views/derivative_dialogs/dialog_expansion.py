from PySide6.QtWidgets import QFormLayout, QDoubleSpinBox, QLabel
from PySide6.QtCore import Qt
from views.derivative_dialogs.derivative_dialog_base import DerivativeDialogBase


class DialogExpansion(DerivativeDialogBase):
    """外扩点集设置对话框，允许用户设置X和Y方向的ppm值"""

    def __init__(self, parent=None, x_ppm_value=3, y_ppm_value=3):
        # 直接存储精确的值，避免浮点精度问题
        self._x_value = x_ppm_value
        self._y_value = y_ppm_value
        super().__init__(parent, "设置外扩值")

    def init_specific_ui(self):
        """初始化特定的UI组件"""
        # 设置描述文本
        self.set_description(
            "设置点集的外扩比例。\n"
            "每个点的坐标将按照设定的比例进行缩放。"
        )

        # 创建表单布局
        form_layout = QFormLayout()
        self.group_layout.addLayout(form_layout)

        # 创建X方向ppm输入框
        self.x_ppm_spinbox = QDoubleSpinBox()
        self.x_ppm_spinbox.setDecimals(2)  # 保留2位小数
        self.x_ppm_spinbox.setSingleStep(0.1)  # 按钮步长0.1
        # 直接使用已存储的精确值
        self.x_ppm_spinbox.setValue(self._x_value)  # 默认值
        self.x_ppm_spinbox.setSuffix(" ppm")
        self.x_ppm_spinbox.setToolTip("X方向的外扩比例，以ppm(百万分之一)为单位")
        form_layout.addRow("X方向外扩:", self.x_ppm_spinbox)

        # 创建Y方向ppm输入框
        self.y_ppm_spinbox = QDoubleSpinBox()
        self.y_ppm_spinbox.setDecimals(2)  # 保留2位小数
        self.y_ppm_spinbox.setSingleStep(0.1)  # 按钮步长0.1
        # 直接使用已存储的精确值
        self.y_ppm_spinbox.setValue(self._y_value)  # 默认值
        self.y_ppm_spinbox.setSuffix(" ppm")
        self.y_ppm_spinbox.setToolTip("Y方向的外扩比例，以ppm(百万分之一)为单位")
        form_layout.addRow("Y方向外扩:", self.y_ppm_spinbox)

        # 添加链接复选框，用于同步X和Y的值
        self.link_values_checkbox = QLabel(
            "<a href='link'>链接XY值</a>"
        )
        self.link_values_checkbox.setTextFormat(Qt.TextFormat.RichText)
        self.link_values_checkbox.setToolTip("点击链接或解除XY值")
        self.link_values_checkbox.linkActivated.connect(self.toggle_link)
        self.is_linked = True
        self.update_link_status()

        # 添加链接状态到表单
        form_layout.addRow("", self.link_values_checkbox)

        # 连接值变化信号
        self.x_ppm_spinbox.valueChanged.connect(self.on_x_value_changed)
        self.y_ppm_spinbox.valueChanged.connect(self.on_y_value_changed)

    def toggle_link(self):
        """切换XY值的链接状态"""
        self.is_linked = not self.is_linked
        self.update_link_status()

        # 如果切换到已链接状态，则同步Y值到X值
        if self.is_linked:
            # 使用存储的精确值进行同步
            self._y_value = self._x_value
            self.y_ppm_spinbox.setValue(self._x_value)

    def update_link_status(self):
        """更新链接状态显示"""
        if self.is_linked:
            self.link_values_checkbox.setText("<a href='link'>已链接XY值</a>")
        else:
            self.link_values_checkbox.setText("<a href='link'>XY值独立</a>")

    def _on_value_changed(self, is_x_spinbox, value):
        """通用的值变化处理方法

        Args:
            is_x_spinbox: 是否是X方向的输入框
            value: 新的值
        """
        # 确定当前和目标的输入框
        current_spinbox = self.x_ppm_spinbox if is_x_spinbox else self.y_ppm_spinbox
        target_spinbox = self.y_ppm_spinbox if is_x_spinbox else self.x_ppm_spinbox

        # 使用字符串解析确保精确值
        text = current_spinbox.text().replace(" ppm", "").strip()
        try:
            # 尝试使用字符串解析获取精确值
            parsed_value = float(text)
            # 更新对应的值
            if is_x_spinbox:
                self._x_value = parsed_value
            else:
                self._y_value = parsed_value
        except ValueError:
            # 如果解析失败，使用提供的value
            if is_x_spinbox:
                self._x_value = value
            else:
                self._y_value = value

        # 如果值已链接，同步更新另一个值
        if self.is_linked:
            # 同步值
            if is_x_spinbox:
                self._y_value = self._x_value
                sync_value = self._x_value
            else:
                self._x_value = self._y_value
                sync_value = self._y_value

            # 暂时断开目标输入框的信号连接，防止循环触发
            target_spinbox.blockSignals(True)
            target_spinbox.setValue(sync_value)
            target_spinbox.blockSignals(False)

    def on_x_value_changed(self, value):
        """X值变化时的处理"""
        self._on_value_changed(True, value)

    def on_y_value_changed(self, value):
        """Y值变化时的处理"""
        self._on_value_changed(False, value)

    def get_expansion_values(self):
        """获取用户设置的外扩值

        Returns:
            tuple: (x_ppm, y_ppm)
        """
        # 返回存储的精确值，而不是从控件获取
        return self._x_value, self._y_value

    def get_params(self):
        """获取对话框的参数字典

        Returns:
            dict: 包含策略参数的字典
        """
        return {
            'x_ppm_value': self._x_value,
            'y_ppm_value': self._y_value
        }