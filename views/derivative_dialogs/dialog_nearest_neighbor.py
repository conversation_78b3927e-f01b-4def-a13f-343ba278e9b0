from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QPushButton, QTableWidget, 
    QTableWidgetItem, QLabel, QGroupBox, QMessageBox, QListWidget,
    QSplitter, QWidget, QCheckBox, QSizePolicy
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QPainter, QPen, QColor

import pandas as pd
import numpy as np
import io

from views.derivative_dialogs.derivative_dialog_base import DerivativeDialogBase


class QuadrilateralPreviewWidget(QLabel):
    """四边形预览控件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(600, 500)  # 增加预览窗口的最小尺寸
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)  # 允许控件在两个方向上扩展
        self.setStyleSheet("background-color: white; border: 1px solid gray;")
        self.quadrilateral_points = None
        self.selected_index = -1  # 选中的四边形索引
        
    def set_quadrilateral(self, points, selected_index=-1):
        """设置四边形点"""
        self.quadrilateral_points = points
        self.selected_index = selected_index
        self.update()  # 强制重绘
        
    def paintEvent(self, event):
        """绘制事件"""
        super().paintEvent(event)
        
        if self.quadrilateral_points is None or len(self.quadrilateral_points) == 0:
            return
            
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 获取控件尺寸
        width = self.width()
        height = self.height()
        
        try:
            # 提取所有点的坐标
            all_points = []
            for quad in self.quadrilateral_points:
                # 根据对象类型获取点坐标
                if isinstance(quad, dict) and 'points' in quad:
                    all_points.extend(quad['points'])
                elif hasattr(quad, 'points'):
                    all_points.extend(quad.points)
                else:
                    all_points.extend(quad)
            
            all_points = np.array(all_points)
            
            # 计算所有四边形的边界框
            min_x = np.min(all_points[:, 0])
            max_x = np.max(all_points[:, 0])
            min_y = np.min(all_points[:, 1])
            max_y = np.max(all_points[:, 1])
            
            # 添加边距
            margin = 10
            scale_x = (width - 2 * margin) / (max_x - min_x) if max_x > min_x else 1
            scale_y = (height - 2 * margin) / (max_y - min_y) if max_y > min_y else 1
            
            # 使用较小的缩放比例，保持纵横比
            scale = min(scale_x, scale_y)
            
            # 计算居中偏移
            offset_x = margin + (width - 2 * margin - scale * (max_x - min_x)) / 2
            offset_y = margin + (height - 2 * margin - scale * (max_y - min_y)) / 2
            
            # 绘制每个四边形
            for quad_idx, quad in enumerate(self.quadrilateral_points):
                # 设置画笔 - 选中的四边形用粗线和不同颜色
                if quad_idx == self.selected_index:
                    pen = QPen(QColor(255, 0, 0))  # 红色
                    pen.setWidth(2)
                else:
                    pen = QPen(QColor(0, 0, 255))  # 蓝色
                    pen.setWidth(1)
                painter.setPen(pen)
                
                # 获取四边形的点坐标
                if isinstance(quad, dict) and 'points' in quad:
                    quad_points = quad['points']
                    panel_name = quad.get('panel_name')
                elif hasattr(quad, 'points'):
                    quad_points = quad.points
                    panel_name = quad.panel_name if hasattr(quad, 'panel_name') else None
                else:
                    quad_points = quad
                    panel_name = None
                
                # 转换坐标
                screen_points = []
                for x, y in quad_points:
                    screen_x = offset_x + scale * (x - min_x)
                    # 注意：屏幕坐标系Y轴向下，需要翻转
                    screen_y = height - (offset_y + scale * (y - min_y))
                    screen_points.append((screen_x, screen_y))
                
                # 绘制四边形
                for i in range(len(quad_points)):
                    j = (i + 1) % len(quad_points)
                    painter.drawLine(
                        int(screen_points[i][0]), int(screen_points[i][1]),
                        int(screen_points[j][0]), int(screen_points[j][1])
                    )
                
                # 绘制四边形编号 - 计算真正的中心点并居中显示
                center_x = sum(p[0] for p in screen_points) / len(screen_points)
                center_y = sum(p[1] for p in screen_points) / len(screen_points)
                
                # 设置文本对齐方式为居中
                painter.setFont(painter.font())
                
                # 获取四边形的标签文本
                if panel_name:
                    text = panel_name
                else:
                    text = f"#{quad_idx+1}"
                
                # 获取文本矩形
                text_rect = painter.fontMetrics().boundingRect(text)
                text_x = int(center_x - text_rect.width() / 2)
                text_y = int(center_y + text_rect.height() / 4)  # 稍微上移一点以视觉居中
                
                # 直接绘制文本，不带背景
                painter.drawText(text_x, text_y, text)
                
        except Exception as e:
            # 绘制错误信息
            painter.setPen(QColor(255, 0, 0))
            painter.drawText(10, 20, f"绘制错误: {str(e)}")


class DialogNearestNeighbor(DerivativeDialogBase):
    """近邻策略设置对话框，允许用户通过剪贴板输入多个四边形坐标"""
    
    def __init__(self, parent=None, quadrilateral_points=None):
        self.quadrilateral_points = []  # 存储多个四边形的点
        self.initial_quadrilateral_points = quadrilateral_points  # 存储初始四边形数据
        super().__init__(parent, "设置近邻策略参数")
        # 设置对话框的初始大小
        self.resize(900, 700)
        
    def init_specific_ui(self):
        """初始化特定的UI组件"""
        # 设置描述文本
        self.set_description(
            "近邻策略：为每个父坐标找到四边形内最近的点。\n"
            "请从Excel复制包含多行四边形坐标的数据（每行8列，分别为X1,Y1,X2,Y2,X3,Y3,X4,Y4）。\n"
            "注意：四边形角点会自动排序，以确保形成有效的四边形。"
        )
        
        # 创建主布局
        main_layout = QVBoxLayout()
        self.group_layout.addLayout(main_layout)
        
        # 创建剪贴板监听按钮和状态标签
        clipboard_layout = QHBoxLayout()
        
        # 根据是否有初始数据设置不同的按钮文本
        if self.initial_quadrilateral_points:
            button_text = "重新监听剪贴板"
        else:
            button_text = "开始监听剪贴板"
            
        self.listen_button = QPushButton(button_text)
        self.listen_button.clicked.connect(self.toggle_clipboard_listening)
        
        # 根据是否有初始数据设置不同的状态文本
        if self.initial_quadrilateral_points:
            status_text = "编辑模式：已加载现有四边形数据"
        else:
            status_text = "请点击按钮开始监听剪贴板"
            
        self.status_label = QLabel(status_text)
        clipboard_layout.addWidget(self.listen_button)
        clipboard_layout.addWidget(self.status_label, 1)
        main_layout.addLayout(clipboard_layout)
        
        # 创建中央区域布局
        central_layout = QHBoxLayout()
        main_layout.addLayout(central_layout, 1)  # 添加伸展因子，使中央区域占据更多空间
        
        # 左侧：四边形列表
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        
        self.quad_list = QListWidget()
        self.quad_list.setMinimumWidth(120)
        self.quad_list.setMaximumWidth(150)
        self.quad_list.currentRowChanged.connect(self.on_quad_selected)
        left_layout.addWidget(QLabel("四边形列表:"))
        left_layout.addWidget(self.quad_list)
        
        # 中央：四边形预览
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        center_layout.setContentsMargins(0, 0, 0, 0)
        
        self.preview_widget = QuadrilateralPreviewWidget()
        center_layout.addWidget(QLabel("四边形预览:"))
        center_layout.addWidget(self.preview_widget, 1)  # 添加伸展因子，使预览区域占据更多空间
        
        # 添加到中央布局
        central_layout.addWidget(left_widget)
        central_layout.addWidget(center_widget, 3)  # 预览区域占据更多水平空间
        
        # 初始化剪贴板监听状态
        self.is_listening = False
        self.clipboard = None
        self.debounce_timer = QTimer(self)
        self.debounce_timer.setSingleShot(True)
        self.debounce_timer.setInterval(200)
        self.debounce_timer.timeout.connect(self.process_clipboard_after_delay)
        
        # 如果有初始四边形数据，加载它们
        if self.initial_quadrilateral_points:
            self.load_initial_quadrilateral_points()
        else:
            # 对话框显示后自动开始监听剪贴板
            QTimer.singleShot(100, self.start_listening)
    
    def toggle_clipboard_listening(self):
        """切换剪贴板监听状态"""
        if self.is_listening:
            self.stop_listening()
        else:
            self.start_listening()
            
    def start_listening(self):
        """开始监听剪贴板"""
        from PySide6.QtWidgets import QApplication
        
        self.is_listening = True
        self.listen_button.setText("停止监听剪贴板")
        self.status_label.setText("正在监听剪贴板...")
        
        # 初始化剪贴板
        self.clipboard = QApplication.clipboard()
        self.clipboard.dataChanged.connect(self.on_clipboard_changed)
        
    def stop_listening(self):
        """停止监听剪贴板"""
        self.is_listening = False
        self.listen_button.setText("开始监听剪贴板")
        self.status_label.setText("剪贴板监听已停止")
        
        if self.clipboard:
            self.clipboard.dataChanged.disconnect(self.on_clipboard_changed)
            
    def on_clipboard_changed(self):
        """剪贴板内容变化时触发，启动消抖定时器"""
        if not self.is_listening:
            return
            
        self.status_label.setText("检测到剪贴板更新...")
        self.debounce_timer.stop()
        self.debounce_timer.start()
        
    def process_clipboard_after_delay(self):
        """消抖延时后处理剪贴板内容"""
        if not self.is_listening or not self.clipboard:
            return
            
        try:
            mime_data = self.clipboard.mimeData()
            
            if mime_data.hasText():
                text = mime_data.text()
                if text and len(text.strip()) > 0:
                    self.process_clipboard_data(text)
                else:
                    self.status_label.setText("剪贴板文本内容为空")
            else:
                self.status_label.setText("剪贴板不包含文本内容")
        except Exception as e:
            self.status_label.setText(f"读取剪贴板失败: {str(e)}")
            
    def process_clipboard_data(self, text):
        """处理剪贴板数据，提取多个四边形坐标"""
        try:
            # 读取数据
            df = pd.read_csv(io.StringIO(text), sep='\t', header=None, engine='python')
            
            # 查找以X/Y开头的列（不区分大小写）
            x_cols = []
            y_cols = []
            panel_col = None  # 存储包含"panel"的列
            
            for col in df.columns:
                # 查找第一个非空单元格作为列名
                for idx in range(min(10, len(df))):  # 只检查前10行
                    val = df.iloc[idx, col]
                    if pd.notna(val) and str(val).strip():
                        col_name = str(val).strip().upper()
                        if col_name.startswith('X'):
                            x_cols.append((col, idx, col_name))
                            break
                        elif col_name.startswith('Y'):
                            y_cols.append((col, idx, col_name))
                            break
                        elif 'PANEL' in col_name:
                            panel_col = (col, idx)
                            break
            
            # 确保找到了至少4对XY列
            if len(x_cols) < 4 or len(y_cols) < 4:
                self.status_label.setText(f"未找到足够的XY列: X列={len(x_cols)}, Y列={len(y_cols)}")
                return
                
            # 按列名排序
            x_cols.sort(key=lambda x: x[2])
            y_cols.sort(key=lambda y: y[2])
            
            # 提取前4对XY列
            x_cols = x_cols[:4]
            y_cols = y_cols[:4]
            
            # 找到所有列的标题行的最大值，作为数据开始的行
            header_rows = [row for _, row, _ in x_cols + y_cols]
            if panel_col:
                header_rows.append(panel_col[1])
            data_start_row = max(header_rows) + 1
            
            # 创建一个简单的类来存储四边形点和panel名称
            class Quadrilateral:
                def __init__(self, points, panel_name=None):
                    self.points = points
                    self.panel_name = panel_name
            
            # 提取每行的四边形坐标
            quads = []
            for row_idx in range(data_start_row, len(df)):
                quad_points = []
                valid_row = True
                
                # 提取这一行的4对XY坐标
                for (x_col, _, _), (y_col, _, _) in zip(x_cols, y_cols):
                    x_val = df.iloc[row_idx, x_col]
                    y_val = df.iloc[row_idx, y_col]
                    
                    # 检查是否有效
                    if pd.isna(x_val) or pd.isna(y_val):
                        valid_row = False
                        break
                        
                    # 转换为浮点数
                    try:
                        x_val = float(x_val)
                        y_val = float(y_val)
                        quad_points.append([x_val, y_val])
                    except (ValueError, TypeError):
                        valid_row = False
                        break
                
                # 如果是有效行且有4个点，添加到四边形列表
                if valid_row and len(quad_points) == 4:
                    # 对四边形角点进行排序，确保形成简单多边形
                    sorted_points = self._sort_quadrilateral_points(np.array(quad_points)).tolist()
                    
                    # 如果存在panel列，获取panel名称
                    panel_name = None
                    if panel_col:
                        panel_val = df.iloc[row_idx, panel_col[0]]
                        if pd.notna(panel_val):
                            panel_name = str(panel_val).strip()
                    
                    # 创建四边形对象
                    quad = Quadrilateral(sorted_points, panel_name)
                    quads.append(quad)
            
            # 确保提取了至少一个四边形
            if not quads:
                self.status_label.setText("未提取到有效的四边形数据")
                return
                
            # 更新四边形列表
            self.quadrilateral_points = quads
            self.update_quad_list()
            
            # 更新预览 - 显示所有四边形，选中第一个
            if len(quads) > 0:
                self.preview_widget.set_quadrilateral(quads, 0)
            
            self.status_label.setText(f"成功提取{len(quads)}个四边形")
            
            # 成功解析数据后自动停止监听
            self.stop_listening()
            
        except Exception as e:
            self.status_label.setText(f"处理剪贴板数据失败: {str(e)}")
    
    def _sort_quadrilateral_points(self, quad):
        """对四边形角点进行排序，确保形成简单多边形
        
        使用凸包算法对点进行排序，如果四边形不是凸的，至少确保它是简单的（不自相交）
        
        Args:
            quad: 四边形的角点坐标，形状为(n, 2)，其中n>=3
            
        Returns:
            numpy.ndarray: 排序后的四边形角点坐标，形状为(n, 2)
        """
        # 如果点数不足，直接返回
        if len(quad) < 3:
            return quad
            
        # 计算质心
        centroid = np.mean(quad, axis=0)
        
        # 按照与质心的连线相对于x轴的角度排序（逆时针）
        def angle_with_centroid(point):
            return np.arctan2(point[1] - centroid[1], point[0] - centroid[0])
            
        # 排序点
        sorted_indices = np.argsort([angle_with_centroid(p) for p in quad])
        sorted_quad = quad[sorted_indices]
        
        return sorted_quad
    
    def update_quad_list(self):
        """更新四边形列表"""
        self.quad_list.clear()
        for i, quad in enumerate(self.quadrilateral_points):
            # 如果四边形有panel名称，则在列表中显示
            panel_name = None
            if isinstance(quad, dict) and 'panel_name' in quad:
                panel_name = quad['panel_name']
            elif hasattr(quad, 'panel_name'):
                panel_name = quad.panel_name
                
            if panel_name:
                self.quad_list.addItem(f"{panel_name}")
            else:
                self.quad_list.addItem(f"四边形 #{i+1}")
        
        # 选中第一个
        if self.quad_list.count() > 0:
            self.quad_list.setCurrentRow(0)
        else:
            # 如果列表为空，清空预览
            self.preview_widget.set_quadrilateral([])
    
    def on_quad_selected(self, row):
        """四边形选择变化时的处理"""
        if row >= 0 and row < len(self.quadrilateral_points):
            # 更新预览，显示所有四边形，但高亮选中的四边形
            self.preview_widget.set_quadrilateral(self.quadrilateral_points, row)
        else:
            # 如果没有选中项，清空预览
            self.preview_widget.set_quadrilateral([])
            
    def get_params(self):
        """获取对话框的参数字典
        
        Returns:
            dict: 包含策略参数的字典
        """
        if not self.quadrilateral_points:
            QMessageBox.warning(self, "参数错误", "请先提供有效的四边形坐标")
            return None
            
        # 将四边形点转换为列表格式，以便正确序列化
        quad_points_list = []
        for i, quad in enumerate(self.quadrilateral_points):
            # 根据对象类型获取点坐标和panel名称
            if isinstance(quad, dict) and 'points' in quad:
                points = quad['points']
                panel_name = quad.get('panel_name')
            elif hasattr(quad, 'points'):
                points = quad.points
                panel_name = quad.panel_name if hasattr(quad, 'panel_name') else None
            else:
                # 如果是numpy数组或列表，直接使用
                points = quad.tolist() if isinstance(quad, np.ndarray) else quad
                panel_name = None
                
            # 如果没有panel名称，使用默认的编号
            if not panel_name:
                panel_name = f"四边形 #{i+1}"
                
            # 始终使用带有panel_name的字典格式
            quad_points_list.append({
                'points': points,
                'panel_name': panel_name
            })
                
        return {
            'quadrilateral_points': quad_points_list
        }
        
    def closeEvent(self, event):
        """对话框关闭事件"""
        self.stop_listening()
        super().closeEvent(event)

    def load_initial_quadrilateral_points(self):
        """加载初始四边形数据"""
        try:
            if self.initial_quadrilateral_points:
                # 确保四边形数据格式正确
                formatted_quads = []
                
                for quad_data in self.initial_quadrilateral_points:
                    # 创建一个简单的类来存储四边形点和panel名称
                    class Quadrilateral:
                        def __init__(self, points, panel_name=None):
                            self.points = points
                            self.panel_name = panel_name
                    
                    # 根据数据格式提取点和panel名称
                    if isinstance(quad_data, dict) and 'points' in quad_data:
                        points = quad_data['points']
                        panel_name = quad_data.get('panel_name', '')
                    else:
                        # 向后兼容，处理旧格式
                        points = quad_data
                        panel_name = f"四边形 #{len(formatted_quads)+1}"
                    
                    # 创建四边形对象
                    quad = Quadrilateral(points, panel_name)
                    formatted_quads.append(quad)
                
                self.quadrilateral_points = formatted_quads
                self.update_quad_list()
                
                # 更新预览 - 显示所有四边形，选中第一个
                if len(self.quadrilateral_points) > 0:
                    self.preview_widget.set_quadrilateral(self.quadrilateral_points, 0)
                
                self.status_label.setText(f"已加载{len(self.quadrilateral_points)}个四边形")
        except Exception as e:
            self.status_label.setText(f"加载初始四边形数据失败: {str(e)}") 