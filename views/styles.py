from PySide6.QtCore import Qt
from PySide6.QtGui import QGuiApplication

class StyleSheet:
    @staticmethod
    def is_dark_mode():
        """Detect if system is using dark mode"""
        # Qt 6.5+ has built-in color scheme detection
        if hasattr(QGuiApplication.styleHints(), 'colorScheme'):
            return QGuiApplication.styleHints().colorScheme() == Qt.ColorScheme.Dark

        # Fallback method for older Qt versions
        # Compare text color lightness with window color lightness
        # If text is lighter than window, it's likely dark mode
        palette = QGuiApplication.palette()
        return palette.color(palette.WindowText).lightness() > palette.color(palette.Window).lightness()

    @staticmethod
    def get_style():
        # Determine if we're in dark mode
        dark_mode = StyleSheet.is_dark_mode()

        # Define color schemes for light and dark modes
        if dark_mode:
            # Dark mode colors
            bg_color = "#1E1E1E"  # Dark background
            text_color = "#FFFFFF"  # White text
            secondary_bg = "#2D2D2D"  # Slightly lighter background
            button_bg = "#0078D7"  # Blue button background
            button_hover = "#1084D9"  # Lighter blue for hover
            button_pressed = "#006CC1"  # Darker blue for pressed
            button_disabled_bg = "#424852"  # 带一点蓝色调的灰色，增强与背景的区分度
            button_disabled_text = "#A0A0A0"  # 更浅的灰色，增强与禁用背景的对比度
            border_color = "#3F3F3F"  # Dark gray border
            highlight_color = "#0078D7"  # Blue highlight
            highlight_text = "#FFFFFF"  # White text on highlight
            input_bg = "#2D2D2D"  # Input background
            menu_hover = "#3F3F3F"  # Menu hover
        else:
            # Light mode colors (mostly original values)
            bg_color = "#FAFAFA"  # Light background
            text_color = "#212121"  # Dark text
            secondary_bg = "#FFFFFF"  # White background
            button_bg = "#42A5F5"  # Blue button background
            button_hover = "#2196F3"  # Darker blue for hover
            button_pressed = "#1E88E5"  # Even darker blue for pressed
            button_disabled_bg = "#E0E0E0"  # Light gray for disabled button
            button_disabled_text = "#9E9E9E"  # Medium gray for disabled text
            border_color = "#bdbdbd"  # Light gray border
            highlight_color = "#2196F3"  # Blue highlight
            highlight_text = "#FFFFFF"  # White text on highlight
            input_bg = "#FFFFFF"  # Input background
            menu_hover = "#e3f2fd"  # Menu hover

        return f"""
        /* For QMainWindow */
        QMainWindow {{
            background-color: {bg_color};
            font-size: 12px;
        }}

        /* For QWidget */
        QWidget {{
            color: {text_color};
            background-color: {bg_color};
            font-size: 12px;
        }}

        /* For QVBoxLayout & QHBoxLayout */
        QVBoxLayout, QHBoxLayout {{
            /* spacing: 10px;*/
            font-size: 12px;
        }}

        /* For QPushButton */
        QPushButton {{
            color: white;
            background-color: {button_bg};
            border: none;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
        }}

        QPushButton:hover {{
            background-color: {button_hover};
        }}

        QPushButton:pressed {{
            background-color: {button_pressed};
        }}

        QPushButton:disabled {{
            color: {button_disabled_text};
            background-color: {button_disabled_bg};
        }}

        QPushButton:checked {{
            background-color: {highlight_color};
            color: {highlight_text};
        }}
        QPushButton:checked:hover {{
            background-color: {button_hover};
        }}

        /* For QLabel */
        QLabel {{
            color: {text_color};
            background-color: {bg_color};
            font-size: 12px;
            min-height: 18px;
        }}

        /* For QLineEdit */
        QLineEdit {{
            color: {text_color};
            border: 1px solid {border_color};
            border-radius: 4px;
            padding: 5px;
            font-size: 12px;
            background-color: {input_bg};
        }}

        QLineEdit:disabled {{
            color: {button_disabled_text};
            border: 1px solid {border_color};
            background-color: {button_disabled_bg};
        }}

        /* For QRadioButton */
        QRadioButton {{
            color: {text_color};
            padding: 5px;
            font-size: 12px;
        }}

        QRadioButton::indicator {{
            border-radius: 6px;
            border: 2px solid {border_color};
        }}

        QRadioButton::indicator:checked {{
            background-color: {highlight_color};
            border: 2px solid {highlight_color};
        }}

        /* For QCheckBox */
        QCheckBox {{
            color: {text_color};
            padding: 5px;
            font-size: 12px;
        }}

        QCheckBox::indicator {{
            width: 12px;
            height: 12px;
            border: 2px solid {border_color};
            border-radius: 2px;
        }}

        QCheckBox::indicator:unchecked:hover {{
            border: 2px solid {highlight_color};
        }}

        QCheckBox::indicator:checked {{
            background-color: {highlight_color};
            border: 2px solid {highlight_color};
        }}

        QCheckBox::indicator:checked:hover {{
            background-color: {button_hover};
            border: 2px solid {button_hover};
        }}

        QCheckBox:disabled {{
            color: {button_disabled_text};
        }}

        QCheckBox::indicator:disabled {{
            border: 2px solid {border_color};
            background-color: {button_disabled_bg};
        }}

        /* For QButtonGroup */
        QButtonGroup {{
            background-color: {secondary_bg};
            border: 1px solid {border_color};
            border-radius: 4px;
            font-size: 12px;
        }}

        /* For QComboBox */
        QComboBox {{
            color: {text_color};
            border: 1px solid {border_color};
            border-radius: 4px;
            padding: 7px;
            font-size: 12px;
            background-color: {input_bg};
        }}

        QComboBox:disabled {{
            color: {button_disabled_text};
            background-color: {button_disabled_bg};
            /* 增强禁用状态的视觉区分度，使用不同的背景色和文字颜色 */
        }}

        /* QComboBox QAbstractItemView */
        QComboBox QAbstractItemView {{
            border: 1px solid {border_color};
            border-radius: 4px;
        }}

        QComboBox QAbstractItemView::item {{
            height: 20px;
            font-size: 12px;
        }}

        /* For QMenu */
        QMenu {{
            color: {text_color};
            background-color: {secondary_bg};
            border: 1px solid {border_color};
            font-size: 12px;
        }}

        QMenu::item:selected {{
            background-color: {menu_hover};
            color: {text_color};
        }}

        QMenu::item:hover {{
            background-color: {menu_hover};
        }}

        /* For QListView */
        QListView {{
            color: {text_color};
            border: 1px solid {border_color};
            border-radius: 4px;
            font-size: 12px;
        }}

        QListView::item {{
            padding: 8px;
            font-size: 12px;
        }}

        /* For QDialog */
        QDialog {{
            color: {text_color};
            background-color: {bg_color};
            border: 1px solid {border_color};
            border-radius: 4px;
            font-size: 12px;
        }}

        /* For QDialogButtonBox */
        QDialogButtonBox {{
            color: {text_color};
            border: 1px solid {border_color};
            font-size: 12px;
        }}

        /* For QMessageBox */
        QMessageBox QLabel {{
            background-color: {bg_color};
            min-height: 50px;
        }}
        """
