from PySide6.QtWidgets import QLineEdit, QFileDialog
from PySide6.QtCore import Qt, QPoint, QStandardPaths, QUrl
from PySide6.QtGui import QMouseEvent


class CustomFileDialog(QFileDialog):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.setOption(QFileDialog.DontUseNativeDialog)
        
        # 添加桌面路径到左侧快捷栏，保留原有的URLs
        desktop_path = QStandardPaths.writableLocation(QStandardPaths.DesktopLocation)
        if desktop_path:
            current_urls = self.sidebarUrls()
            desktop_url = QUrl.fromLocalFile(desktop_path)
            if desktop_url not in current_urls:  # 避免重复添加
                current_urls.append(desktop_url)
                self.setSidebarUrls(current_urls)

    @staticmethod
    def getOpenFileName(parent=None, caption="", dir="", filter="",
                        initialFilter="", options=None):
        dialog = CustomFileDialog(parent)
        dialog.setWindowTitle(caption)
        dialog.setDirectory(dir)
        dialog.setNameFilter(filter)
        dialog.setFileMode(QFileDialog.ExistingFile)
        dialog.setAcceptMode(QFileDialog.AcceptOpen)

        if options:
            dialog.setOptions(options)

        if dialog.exec() == QFileDialog.Accepted:
            selected_files = dialog.selectedFiles()
            if selected_files:
                return selected_files[0], dialog.selectedNameFilter()
        return "", ""

    @staticmethod
    def getSaveFileName(parent=None, caption="", dir="", filter="",
                        initialFilter="", options=None):
        dialog = CustomFileDialog(parent)
        dialog.setWindowTitle(caption)
        dialog.setDirectory(dir)
        dialog.setNameFilter(filter)
        dialog.setDefaultSuffix("")
        dialog.setAcceptMode(QFileDialog.AcceptSave)

        if options:
            dialog.setOptions(options)

        # 解析过滤器以获取默认后缀
        if filter:
            first_filter = filter.split(';;')[0]
            suffix = first_filter.split('*')[-1].strip(')')
            if suffix:
                dialog.setDefaultSuffix(suffix.lstrip('.'))

        if dialog.exec() == QFileDialog.Accepted:
            selected_files = dialog.selectedFiles()
            if selected_files:
                selected_file = selected_files[0]

                # 获取当前选择的过滤器中的后缀
                current_filter = dialog.selectedNameFilter()
                current_suffix = current_filter.split('*')[-1].strip(')')
                current_suffix = current_suffix.lstrip('.')

                # 如果文件名不是以当前后缀结尾，就添加后缀
                if not selected_file.endswith(f".{current_suffix}"):
                    selected_file = f"{selected_file}.{current_suffix}"

                return selected_file, current_filter
        return "", ""


class CustomLineEdit(QLineEdit):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMouseTracking(True)

    def mouseMoveEvent(self, event):
        if event.buttons() & Qt.LeftButton:
            rect = self.rect()
            mouse_pos = event.position().toPoint()

            x = max(0, min(mouse_pos.x(), rect.width() - 1))
            y = rect.height() // 2

            if mouse_pos != QPoint(x, y):
                new_pos = QPoint(x, y)
                new_event = QMouseEvent(
                    event.type(),
                    new_pos,
                    event.globalPosition(),
                    event.button(),
                    event.buttons(),
                    event.modifiers()
                )
                super().mouseMoveEvent(new_event)
            else:
                super().mouseMoveEvent(event)
        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton:
            rect = self.rect()
            mouse_pos = event.position().toPoint()

            x = max(0, min(mouse_pos.x(), rect.width() - 1))
            y = rect.height() // 2

            new_pos = QPoint(x, y)
            new_event = QMouseEvent(
                event.type(),
                new_pos,
                event.globalPosition(),
                event.button(),
                event.buttons(),
                event.modifiers()
            )
            super().mouseReleaseEvent(new_event)
        else:
            super().mouseReleaseEvent(event)
