name: Compile main.py with Nuitka

permissions:
  contents: write

on:
  workflow_dispatch:

jobs:
  version:
    runs-on: windows-latest
    outputs:
      new_version: ${{ steps.version.outputs.NEW_VERSION }}
      version_number: ${{ steps.version.outputs.version }}
      tag_name: ${{ steps.version.outputs.tag_name }}
      file_version: ${{ steps.set_file_version.outputs.file_version }}
      release_notes: ${{ steps.generate_release_notes.outputs.RELEASE_NOTES }}
      file_name_base: ${{ steps.version.outputs.file_name_base }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get version from latest tag
        id: version
        shell: bash
        run: |
          # 获取当前提交的tag（按版本号降序取最新）
          CURRENT_TAG=$(git tag --points-at HEAD --sort=-v:refname | head -n 1)
          # 获取全仓库最新tag
          PREV_TAG=$(git tag --list 'v*' --sort=-v:refname | grep -vx "$CURRENT_TAG" | head -n 1)
          
          # 处理空值情况
          if [ -z "$PREV_TAG" ]; then
            PREV_TAG="v0.0.0"
          fi

          if [ -n "$CURRENT_TAG" ]; then
            NEW_VERSION=$CURRENT_TAG
          else
            # 解析版本号并递增补丁版本
            VERSION=${PREV_TAG#v}
            IFS='.' read -ra VER <<< "$VERSION"
            MAJOR=${VER[0]:-0}
            MINOR=${VER[1]:-0}
            PATCH=${VER[2]:-0}
            NEW_VERSION="v$MAJOR.$MINOR.$((PATCH + 1))"
          fi

          echo "NEW_VERSION=$NEW_VERSION" >> $GITHUB_OUTPUT
          echo "version=${NEW_VERSION#v}" >> $GITHUB_OUTPUT
          echo "tag_name=$NEW_VERSION" >> $GITHUB_OUTPUT
          echo "PREV_TAG=$PREV_TAG" >> $GITHUB_OUTPUT
          echo "file_name_base=Measurement Point Selector $NEW_VERSION" >> $GITHUB_OUTPUT

          # 调试输出
          echo "CURRENT_TAG=$CURRENT_TAG"
          echo "PREV_TAG=$PREV_TAG"
          echo "NEW_VERSION=$NEW_VERSION"
          echo "file_name_base=Measurement Point Selector $NEW_VERSION"

      - name: Set file version (four-part format)
        id: set_file_version
        shell: bash
        run: |          
          NEW_VERSION="${{ steps.version.outputs.NEW_VERSION }}"
          echo "NEW_VERSION=$NEW_VERSION"
          VERSION_NUM="${NEW_VERSION#v}"
          IFS='.' read -ra PARTS <<< "$VERSION_NUM"
          while [ "${#PARTS[@]}" -lt 4 ]; do
            PARTS+=("0")
          done
          FILE_VERSION="${PARTS[0]}.${PARTS[1]}.${PARTS[2]}.${PARTS[3]}"
          echo "file_version=$FILE_VERSION" >> $GITHUB_OUTPUT

          echo "file_version=$FILE_VERSION"

      - name: Generate Release Notes
        id: generate_release_notes
        shell: bash
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          # 通过 GitHub API 获取最新 release 的 tag
          LAST_RELEASE_TAG=$(curl -s -H "Authorization: token ${GITHUB_TOKEN}" \
            -H "Accept: application/vnd.github.v3+json" \
            https://api.github.com/repos/${GITHUB_REPOSITORY}/releases/latest | jq -r '.tag_name')

          if [ -z "$LAST_RELEASE_TAG" ] || [ "$LAST_RELEASE_TAG" = "null" ]; then
            echo "未找到上一次正式 release，获取所有 commit。"
            RAW_COMMITS=$(git log --reverse --format="%B")
          else
            echo "上一次正式 release 的 tag: $LAST_RELEASE_TAG"
            RAW_COMMITS=$(git log ${LAST_RELEASE_TAG}..HEAD --reverse --format="%B")
          fi

          COMMITS=""
          while IFS= read -r line; do
            if [ -n "$line" ]; then
              COMMITS+="- $line"$'\n'
            fi
          done <<< "$RAW_COMMITS"

          RELEASE_NOTES=$'**Changes in this release**:\n'"$COMMITS"
          echo "RELEASE_NOTES<<EOF" >> $GITHUB_OUTPUT
          echo "$RELEASE_NOTES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

          # 输出到日志进行调试
          echo "===== Generated Release Notes ====="
          echo -e "$RELEASE_NOTES"
          echo "==================================="

  build:
    needs: version
    runs-on: windows-latest
    outputs:
      files: ${{ steps.set_output.outputs.files }}
    strategy:
      matrix:
        include:
          - python_version: '3.12'
            requirements_file: 'requirements.txt'
            file_suffix: ''
          - python_version: '3.8'
            requirements_file: 'requirements.txt'
            file_suffix: ' win7'

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python_version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python_version }}
          architecture: 'x64'
          cache: 'pip'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r ${{ matrix.requirements_file }} imageio

      - name: Build executable with Nuitka
        uses: Nuitka/Nuitka-Action@main
        with:
          nuitka-version: 2.6.9
          script-name: main.py
          mode: app
          enable-plugins: pyside6
          windows-icon-from-ico: resources/logo.png
          onefile-windows-splash-screen-image: resources/logo.png
          windows-console-mode: disable
          include-data-dir: resources=resources
          output-file: ${{ needs.version.outputs.file_name_base }}${{ matrix.file_suffix }}.exe
          output-dir: build
          product-name: Measurement Point Selector
          file-version: ${{ needs.version.outputs.file_version }}
          product-version: ${{ needs.version.outputs.file_version }}
          file-description: Measurement Point Selector
          copyright: Copyright © 2025 LaiWX

      # 保存构建文件到工作区
      - name: Upload build to workspace
        uses: actions/upload-artifact@v4
        with:
          name: exe-${{ matrix.python_version }}
          path: build/${{ needs.version.outputs.file_name_base }}${{ matrix.file_suffix }}.exe
          retention-days: 1

  release:
    needs: [version, build]
    runs-on: windows-latest
    steps:
      - name: Download all builds
        uses: actions/download-artifact@v4
        with:
          path: builds
          merge-multiple: true

      - name: Create Release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ needs.version.outputs.tag_name }}
          name: ${{ needs.version.outputs.tag_name }}
          body: ${{ needs.version.outputs.release_notes }}
          draft: false
          prerelease: false
          files: builds/*
