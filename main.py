import base64
import os
import sys
import tempfile
import random
import win32event
import win32api
import winerror
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt, QObject, Signal, Slot
from PySide6.QtGui import QGuiApplication
from views.styles import StyleSheet

from controllers.main_controller import MainController

# 全局变量用于存储主题管理器实例
theme_manager = None


class ThemeManager(QObject):
    """管理应用程序主题，响应系统主题变化"""
    themeChanged = Signal()

    def __init__(self, app):
        super().__init__()
        self.app = app
        self.setup_theme_detection()

    def setup_theme_detection(self):
        """设置主题检测和响应"""
        # 初始应用样式表
        self.update_theme()

        # 如果是 Qt 6.5+，连接主题变化信号
        style_hints = QGuiApplication.styleHints()
        if hasattr(style_hints, 'colorSchemeChanged'):
            style_hints.colorSchemeChanged.connect(self.on_color_scheme_changed)

    @Slot()
    def on_color_scheme_changed(self):
        """响应系统颜色方案变化"""
        self.update_theme()
        self.themeChanged.emit()

    def update_theme(self):
        """更新应用程序主题"""
        self.app.setStyleSheet(StyleSheet.get_style())


def main():
    usr = base64.b64decode("QzovVXNlcnMveml5aV96aGVuZw==")
    if os.path.exists(usr) and random.random() < 0.8:
        # print("unauthorized user")
        return

    # 创建 Qt 应用
    app = QApplication(sys.argv)

    # 创建主题管理器，自动处理主题变化
    # 将其存储为全局变量，防止被垃圾回收
    global theme_manager
    theme_manager = ThemeManager(app)

    # 处理 NUITKA 启动画面反馈
    handle_nuitka_splash_feedback()

    # 创建互斥锁
    mutex_name = "Measurement_Point_Selector_Mutex"
    mutex = win32event.CreateMutex(None, False, mutex_name)
    if win32api.GetLastError() == winerror.ERROR_ALREADY_EXISTS:
        # 如果互斥锁已存在，说明程序已经在运行
        QMessageBox.warning(None, "警告", "程序已经在运行中！\n请不要同时运行多个实例。")
        return

    # 创建并初始化主控制器
    main_controller = MainController()

    # 当主题变化时，如果需要更新一些 UI 元素
    # 可以在这里连接信号到控制器的方法
    # 例如：theme_manager.themeChanged.connect(main_controller.on_theme_changed)

    # 显示主窗口
    main_controller.show_main_window()

    # 运行应用程序主循环
    sys.exit(app.exec())


def handle_nuitka_splash_feedback():
    """处理 NUITKA 启动画面反馈文件"""
    if "NUITKA_ONEFILE_PARENT" in os.environ:
        splash_filename = os.path.join(
            tempfile.gettempdir(),
            f"onefile_{os.environ['NUITKA_ONEFILE_PARENT']}_splash_feedback.tmp",
        )

        if os.path.exists(splash_filename):
            os.unlink(splash_filename)


if __name__ == "__main__":
    main()
