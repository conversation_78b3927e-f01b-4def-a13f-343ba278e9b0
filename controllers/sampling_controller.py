import logging
from PySide6.QtCore import QThread, Signal, QObject, QTimer
from PySide6.QtWidgets import QMessageBox

from models.sampling_model import SamplingModel
from utils.point_tools import edge_distribution_check
from views.sampling_window import SamplingWindow, SortMode, SamplingMethod

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SamplingWorker(QThread):
    """处理采样的工作线程"""
    finished = Signal(object)
    error = Signal(str)

    def __init__(self, sampling_model, params, parent=None):
        super().__init__(parent)
        self.sampling_model = sampling_model
        self.params = params
        self._is_cancelled = False
        logger.debug(f"采样工作线程创建: {id(self)}")

    def cancel(self):
        """标记取消状态"""
        logger.debug(f"采样工作线程收到取消请求: {id(self)}")
        self._is_cancelled = True
        # 通知model取消操作
        if hasattr(self.sampling_model, 'cancel_operation'):
            self.sampling_model.cancel_operation()

    def run(self):
        try:
            # 重置取消标志
            self._is_cancelled = False
            
            # 检查是否已经被取消
            if self._is_cancelled:
                logger.debug(f"采样工作线程在启动时已被取消: {id(self)}")
                return

            result = self.sampling_model.sample_points(
                self.params['sampling_numerical'],
                self.params['sampling_unit'],
                self.params['method'],
                self.params['prefer']
            )

            # 再次检查是否被取消
            if self._is_cancelled:
                logger.debug(f"采样工作线程在完成时已被取消: {id(self)}")
                return

            self.finished.emit(result)
        except Exception as e:
            logger.error(f"采样错误: {str(e)}")
            self.error.emit(str(e))
        finally:
            logger.debug(f"SamplingWorker finishing: {id(self)}")


class SortWorker(QThread):
    """处理排序的工作线程"""
    finished = Signal(object)
    error = Signal(str)

    def __init__(self, sampling_model, sort_mode_list, parent=None):
        super().__init__(parent)
        self.sampling_model = sampling_model
        self.sort_mode_list = sort_mode_list
        self._is_cancelled = False
        logger.debug(f"排序工作线程创建: {id(self)}")

    def cancel(self):
        """标记取消状态"""
        logger.debug(f"排序工作线程收到取消请求: {id(self)}")
        self._is_cancelled = True
        # 通知model取消操作
        if hasattr(self.sampling_model, 'cancel_operation'):
            self.sampling_model.cancel_operation()

    def run(self):
        try:
            # 重置取消标志
            self._is_cancelled = False
            
            # 检查是否已经被取消
            if self._is_cancelled:
                logger.debug(f"排序工作线程在启动时已被取消: {id(self)}")
                return

            result = self.sampling_model.sort_points(self.sort_mode_list)

            # 再次检查是否被取消
            if self._is_cancelled:
                logger.debug(f"排序工作线程在完成时已被取消: {id(self)}")
                return

            self.finished.emit(result)
        except Exception as e:
            logger.error(f"排序错误: {str(e)}")
            self.error.emit(str(e))
        finally:
            logger.debug(f"SortWorker finishing: {id(self)}")


class SamplingController(QObject):
    def __init__(self, data, update_callback, edit_index=None, previous_params=None, sampled_df=None,
                 shot_id_col=None, x_col=None, y_col=None, previous_name=None):
        """初始化控制器"""
        super().__init__()
        self.initialize_components(data, sampled_df, shot_id_col, x_col, y_col, previous_name)
        self.setup_controller_state(update_callback, edit_index, previous_params)
        self.setup_connections()
        self.initialize_window()

        # 添加标志位
        self.is_sampling = False
        self.is_sorting = False
        self.pending_cleanup = set()  # 用于跟踪待清理的线程

    def initialize_components(self, data, sampled_df, shot_id_col, x_col, y_col, previous_name):
        """初始化组件"""
        self.sampling_model = SamplingModel(data, sampled_df, shot_id_col, x_col, y_col)
        self.sampling_window = SamplingWindow(shot_id_col is not None, previous_name, self.on_plot_clicked)
        self.sampling_worker = None
        self.sort_worker = None

    def setup_controller_state(self, update_callback, edit_index, previous_params):
        """设置控制器状态"""
        self.update_callback = update_callback
        self.edit_index = edit_index
        self.previous_params = previous_params
        self.is_closing = False

    def setup_connections(self):
        """设置信号连接"""
        # 视图信号连接
        self.sampling_window.refresh.connect(self.update_sample)
        self.sampling_window.confirm_button.clicked.connect(self.confirm_sample)
        self.sampling_window.closeEvent = self.on_window_closed
        self.sampling_window.manual_mode_btn.clicked.connect(self.on_manual_mode_changed)

        # 采样参数变动自动采样
        self.sampling_window.sampling_params_changed.connect(self.update_sample)

        # 排序参数变动自动排序
        self.sampling_window.sort_params_changed.connect(self.update_sort)
        
        # 排序模式变更处理
        self.sampling_window.sort_combo.currentTextChanged.connect(self.on_sort_mode_changed)

    def initialize_window(self):
        """初始化窗口"""
        self.update_plot()
        if not self.restore_previous_params():
            self.check_distribution()

    def update_plot(self, is_click=False):
        """更新图表显示"""
        plot_data = self.sampling_model.get_plot_data()
        self.sampling_window.update_plot(
            plot_data,
            self.sampling_model,
            is_click=is_click
        )

    def on_plot_clicked(self):
        """处理图表点击事件"""
        try:
            if len(self.sampling_model.sampled_coords) == 0:
                self.sampling_window.enable_confirm(False)
            else:
                self.sampling_window.enable_confirm(True)
            sort_mode = self.sampling_window.get_sort_mode_list().get('mode')
            if sort_mode and sort_mode != '不排序':
                self.update_sort(is_click=True)
            else:
                self.update_plot(is_click=True)
        except Exception as e:
            logger.error(f"点击处理错误: {str(e)}")
            self.sampling_window.set_status(f"{str(e)}", True)
            self.sampling_model.sorted_flag = False
            self.sampling_window.enable_confirm(False)
            self.update_plot(is_click=True)

    def show_window(self):
        """显示窗口"""
        self.sampling_window.show()
        self.sampling_window.windowHandle().requestActivate()

    def close_window(self):
        """关闭窗口"""
        if not self.is_closing:
            self.is_closing = True
            self._stop_workers()
            self.sampling_window.close()

    def _stop_workers(self):
        """安全停止线程"""
        workers_to_stop = []
        if self.sampling_worker is not None:
            workers_to_stop.append(self.sampling_worker)
        if self.sort_worker is not None:
            workers_to_stop.append(self.sort_worker)

        for worker in workers_to_stop:
            try:
                if worker.isRunning():
                    logger.debug(f"正在停止工作线程: {id(worker)}")
                    worker.cancel()
                    worker.quit()
                    if not worker.wait(1000):
                        logger.warning(f"工作线程终止超时，强制终止: {id(worker)}")
                        worker.terminate()
                        worker.wait()
                self._cleanup_worker(worker)
            except Exception as e:
                logger.error(f"停止工作线程时出错: {str(e)}")

    def on_window_closed(self, event):
        """窗口关闭事件处理"""
        if not self.is_closing:
            self.is_closing = True
            self.update_callback(None)
        event.accept()

    def check_distribution(self):
        """检查点位分布"""
        try:
            edge_count, total_count = self._calculate_distribution()
            self._update_distribution_ui(edge_count, total_count)
        except Exception as e:
            logger.error(f"分布检查错误: {str(e)}")
            self.sampling_window.set_status("分布检查失败", True)
            self.sampling_model.clean()
            self.update_plot()

    def _calculate_distribution(self):
        """计算分布情况"""
        if self.sampling_model.shot_id_col_name and self.sampling_window.sampling_unit_combo.currentText() == "每Shot":
            return self._calculate_shot_distribution()
        return self._calculate_global_distribution()

    def _calculate_shot_distribution(self):
        """计算每Shot的分布"""
        edge_count = sum(1 for _, group in self.sampling_model.data.groupby(self.sampling_model.shot_id_col_name)
                         if edge_distribution_check(
            group[[self.sampling_model.x_col_name, self.sampling_model.y_col_name]].values))
        total_count = self.sampling_model.data[self.sampling_model.shot_id_col_name].nunique()
        return edge_count, total_count

    def _calculate_global_distribution(self):
        """计算全局分布"""
        coords = self.sampling_model.get_coords()
        edge_count = 1 if edge_distribution_check(coords) else 0
        return edge_count, 1

    def _update_distribution_ui(self, edge_count, total_count):
        """更新分布UI显示"""
        if edge_count / total_count > 0.5:
            self.sampling_window.set_edge_distribution(edge_count, total_count)
        else:
            self.sampling_window.set_grid_distribution(total_count - edge_count, total_count)

    def update_sample(self):
        """更新采样结果"""
        try:
            # 检查是否进行过手动采样，如果是则提示用户确认
            if self.sampling_model.has_manual_sampling and self.sampling_model.sampled_coords is not None and len(self.sampling_model.sampled_coords) > 0:
                reply = QMessageBox.question(
                    self.sampling_window,
                    "确认操作",
                    "自动采样将覆盖当前手动选点结果，是否继续？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                if reply == QMessageBox.No:
                    # 如果用户选择"否"，则还原UI上的采样参数
                    self.restore_previous_params(self_restore=True)
                    return

            # 如果已有采样任务在进行中，先终止它
            if self.sampling_worker is not None and self.sampling_worker.isRunning():
                logger.debug("检测到正在进行的采样任务，准备终止...")
                self.sampling_worker.cancel()
                self.sampling_worker.quit()
                if not self.sampling_worker.wait(1000):
                    logger.warning(f"采样任务终止超时: {id(self.sampling_worker)}")
                    self.sampling_worker.terminate()
                    self.sampling_worker.wait()
                self._schedule_worker_cleanup(self.sampling_worker)

            self._start_sampling_worker()
            self.previous_params = self.get_current_params()
        except ValueError as e:
            logger.warning(f"采样参数错误: {str(e)}")
            self.sampling_window.set_status(str(e), True)
            self.sampling_model.clean()
            self.update_plot()
        except Exception as e:
            logger.error(f"采样处理错误: {str(e)}")
            self._handle_sampling_error(str(e))

    def _start_sampling_worker(self):
        """启动采样工作线程"""
        logger.debug(f"准备启动新的采样任务...")
        
        # 确保之前的worker已经被清理
        if self.sampling_worker is not None:
            if self.sampling_worker in self.pending_cleanup:
                logger.debug(f"等待之前的采样任务清理完成: {id(self.sampling_worker)}")
                self.sampling_worker.wait()
                self.sampling_worker = None
                self.pending_cleanup.discard(self.sampling_worker)
        
        params = self._prepare_sampling()
        self.sampling_worker = SamplingWorker(self.sampling_model, params, self)
        self.sampling_worker.finished.connect(self._on_sampling_finished)
        self.sampling_worker.error.connect(self._on_sampling_error)
        # 连接线程完成信号到清理函数
        self.sampling_worker.finished.connect(lambda: self._schedule_worker_cleanup(self.sampling_worker))
        logger.debug(f"启动采样任务: {id(self.sampling_worker)}")
        self.is_sampling = True  # 在启动前设置标志
        self.sampling_worker.start()

    def _on_sampling_finished(self, result):
        """采样完成回调"""
        try:
            self.sampling_model.sampled_coords = result
            # 自动采样完成后，重置手动采样标志
            self.sampling_model.has_manual_sampling = False
            self.update_plot()
            self.sampling_window.set_status("采样预览完成，请确认或调整参数重新采样")
            self.sampling_window.enable_confirm(True)

            # 检查是否需要自动排序
            sort_mode = self.sampling_window.get_sort_mode_list().get('mode')
            if sort_mode and sort_mode != SortMode.NONE.value:
                self.update_sort()
        except Exception as e:
            logger.error(f"处理采样结果时出错: {str(e)}")

    def _on_sampling_error(self, error_msg):
        """采样错误处理"""
        self._handle_sampling_error(error_msg)

    def _handle_sampling_error(self, error_msg):
        """处理采样错误"""
        self.sampling_window.set_status(f"采样出错: {error_msg}", True)
        self.sampling_model.clean()
        self.sampling_window.enable_confirm(False)
        self.update_plot()

    def update_sort(self, is_click=False):
        """更新排序结果"""
        try:
            # 如果已有排序任务在进行中，先终止它
            if self.sort_worker is not None and self.sort_worker.isRunning():
                logger.debug("检测到正在进行的排序任务，准备终止...")
                self.sort_worker.cancel()
                self.sort_worker.quit()
                if not self.sort_worker.wait(1000):
                    logger.warning(f"排序任务终止超时: {id(self.sort_worker)}")
                    self.sort_worker.terminate()
                    self.sort_worker.wait()
                self._schedule_worker_cleanup(self.sort_worker)

            sort_mode_list = self.sampling_window.get_sort_mode_list()
            if sort_mode_list['mode'] == SortMode.NONE.value:
                self.sampling_model.sorted_flag = False
                self.update_plot(is_click=is_click)
                return

            self._start_sort_worker()
            self.previous_params = self.get_current_params()
        except Exception as e:
            logger.error(f"排序处理错误: {str(e)}")
            self._handle_sort_error(str(e))

    def _start_sort_worker(self):
        """启动排序工作线程"""
        logger.debug(f"准备启动新的排序任务...")
        
        # 确保之前的worker已经被清理
        if self.sort_worker is not None:
            if self.sort_worker in self.pending_cleanup:
                logger.debug(f"等待之前的排序任务清理完成: {id(self.sort_worker)}")
                self.sort_worker.wait()
                self.sort_worker = None
                self.pending_cleanup.discard(self.sort_worker)
        
        sort_mode_list = self._prepare_sorting()
        self.sort_worker = SortWorker(self.sampling_model, sort_mode_list, self)
        self.sort_worker.finished.connect(self._on_sort_finished)
        self.sort_worker.error.connect(self._on_sort_error)
        # 连接线程完成信号到清理函数
        self.sort_worker.finished.connect(lambda: self._schedule_worker_cleanup(self.sort_worker))
        logger.debug(f"启动排序任务: {id(self.sort_worker)}")
        self.is_sorting = True  # 在启动前设置标志
        self.sort_worker.start()

    def _on_sort_finished(self, result):
        """排序完成回调"""
        try:
            self.sampling_model.sampled_coords = result
            sort_mode_list = self.sampling_window.get_sort_mode_list()
            self.sampling_model.sorted_flag = sort_mode_list['mode'] != SortMode.NONE.value
            self.update_plot()
            self.sampling_window.enable_confirm(True)
            self.sampling_window.set_status("排序完成，请确认或调整参数重新采样")
        except Exception as e:
            logger.error(f"处理排序结果时出错: {str(e)}")

    def _on_sort_error(self, error_msg):
        """排序错误处理"""
        self._handle_sort_error(error_msg)

    def _handle_sort_error(self, error_msg):
        """处理排序错误"""
        self.sampling_window.enable_confirm(False)
        self.sampling_window.set_status(f"排序出错: {error_msg}", True)
        self.sampling_model.sorted_flag = False
        self.update_plot()

    def on_manual_mode_changed(self, checked):
        try:
            self.sampling_model.manual_mode = checked  # 确保模型状态同步
            if checked:
                self._enter_manual_mode()
            else:
                self._exit_manual_mode()
            # 更新按钮文本（如果需要）
            self.sampling_window.manual_mode_btn.setText("退出手动选点" if checked else "进入手动选点")
        except Exception as e:
            logger.error(f"手动模式切换错误: {str(e)}")
            self.sampling_window.set_status(f"模式切换失败: {str(e)}", True)

    def _handle_manual_mode(self, checked):
        """处理手动模式逻辑"""
        self.sampling_model.manual_mode = checked
        if checked:
            self._enter_manual_mode()
        else:
            self._exit_manual_mode()

    def _enter_manual_mode(self):
        """进入手动模式"""
        self.sampling_window.set_status("已进入手动选点模式，点击图中点位可切换选择状态")
        sort_mode_list = self.sampling_window.get_sort_mode_list()
        if sort_mode_list['mode'] not in [None, '不排序']:
            self.sampling_model.sorted_flag = True

    def _exit_manual_mode(self):
        """退出手动模式"""
        self.sampling_window.set_status("已退出手动选点模式")

    def confirm_sample(self):
        """确认采样结果"""
        if not self.is_closing:
            try:
                self._handle_confirmation()
            except Exception as e:
                logger.error(f"确认采样错误: {str(e)}")
                self.sampling_window.set_status("确认采样失败", True)

    def _handle_confirmation(self):
        """处理确认逻辑"""
        self.is_closing = True
        result = self.prepare_result()
        result['params'] = self.get_current_params()
        self.sampling_window.close()
        self.update_callback(result)

    def prepare_result(self):
        """准备返回结果"""
        return {
            'id': self.edit_index if self.edit_index is not None else -1,
            'original_data': self.sampling_model.data,
            'shot_id_col_name': self.sampling_model.shot_id_col_name,
            'x_col_name': self.sampling_model.x_col_name,
            'y_col_name': self.sampling_model.y_col_name,
            'sampled_df': self.sampling_model.sampled_df,
        }

    def get_current_params(self):
        """获取当前参数"""
        return {
            'sampling_numerical': self.sampling_window.get_sampling_numerical(),
            'method': self.sampling_window.get_sampling_method(),
            'sampling_unit': self.sampling_window.get_sampling_unit(),
            'prefer': self.sampling_window.get_sampling_prefer(),
            'sort_mode_list': self.sampling_window.get_sort_mode_list(),
            'has_manual_sampling': self.sampling_model.has_manual_sampling,
        }

    def restore_previous_params(self, self_restore=False):
        """恢复之前的参数"""
        if not self.previous_params:
            return False

        try:
            # 暂时断开信号连接
            self.sampling_window.sampling_params_changed.disconnect(self.update_sample)
            self.sampling_window.sort_params_changed.disconnect(self.update_sort)

            success = self.sampling_window.restore_params(self.previous_params)
            
            if success:
                # 设置排序标志
                sort_mode_list = self.sampling_window.get_sort_mode_list()
                self.sampling_model.sorted_flag = sort_mode_list and sort_mode_list.get('mode') != SortMode.NONE.value

                if not self_restore:
                    # 恢复手动采样标志
                    if 'has_manual_sampling' in self.previous_params:
                        self.sampling_model.has_manual_sampling = self.previous_params['has_manual_sampling']

                    # 更新图表显示
                    self.update_plot()
                return True
            return False
        except Exception as e:
            logger.error(f"恢复参数错误: {str(e)}")
            return False
        finally:
            # 重新连接信号
            self.sampling_window.sampling_params_changed.connect(self.update_sample)
            self.sampling_window.sort_params_changed.connect(self.update_sort)

    def _schedule_worker_cleanup(self, worker):
        """安排工作线程的清理"""
        if worker is None:
            return
            
        logger.debug(f"安排清理工作线程: {id(worker)}")
        self.pending_cleanup.add(worker)
        # 使用 singleShot 延迟清理
        QTimer.singleShot(0, lambda: self._cleanup_worker(worker))

    def _cleanup_worker(self, worker):
        """清理工作线程"""
        if worker is None or worker not in self.pending_cleanup:
            return

        try:
            if worker.isRunning():
                logger.debug(f"工作线程仍在运行，等待完成: {id(worker)}")
                worker.wait()
            
            logger.debug(f"清理工作线程: {id(worker)}")
            worker.deleteLater()
            self.pending_cleanup.discard(worker)
            
            # 重置对应的引用和状态
            if worker == self.sampling_worker:
                self.sampling_worker = None
                self.is_sampling = False
            elif worker == self.sort_worker:
                self.sort_worker = None
                self.is_sorting = False
        except Exception as e:
            logger.error(f"清理工作线程时出错: {str(e)}")

    def __del__(self):
        """析构函数"""
        logger.debug("SamplingController 开始清理...")
        self._stop_workers()
        # 确保所有待清理的线程都被处理
        for worker in list(self.pending_cleanup):
            self._cleanup_worker(worker)
        logger.debug("SamplingController 清理完成")

    def _prepare_sampling(self):
        """准备采样参数"""
        self.sampling_model.sorted_flag = False
        sampling_params = self.sampling_window.get_sampling_numerical()
        
        if sampling_params.get('use_sampling_size'):
            if sampling_params.get('sampling_size_x') is None or sampling_params.get('sampling_size_y') is None:
                raise ValueError("请输入有效的采样尺寸")
        else:
            if sampling_params.get('num_points') is None:
                raise ValueError("请输入有效的目标点数")

        self.sampling_window.set_status("采样处理中...")
        return {
            'sampling_numerical': sampling_params,
            'sampling_unit': self.sampling_window.get_sampling_unit(),
            'method': self.sampling_window.get_sampling_method(),
            'prefer': self.sampling_window.get_sampling_prefer()
        }

    def _prepare_sorting(self):
        """准备排序参数"""
        self.sampling_window.set_status("排序处理中...")
        return self.sampling_window.get_sort_mode_list()

    def on_sort_mode_changed(self, mode: str):
        """处理排序模式变更
        
        Args:
            mode: 排序模式
        """
        try:
            # 更新UI状态
            enable_custom = mode == SortMode.CUSTOM.value
            self.sampling_window.update_sort_controls_state(enable_custom)

            # 处理自定义模式的默认值
            if enable_custom:
                if not self.sampling_window.sort_tolerance.text().strip():
                    self.sampling_window.sort_tolerance.setText("0.5")
            else:
                self.sampling_window.sort_tolerance.clear()

            # 处理TP&OVL模式
            if mode == SortMode.TP_OVL.value:
                self.handle_tp_ovl_mode()
            else:
                # 其他模式正常发射排序参数变化信号
                self.sampling_window.sort_params_changed.emit()
                
                # 如果是"不排序"模式，仅在有有效采样点数据时才启用确认按钮
                if mode == SortMode.NONE.value:
                    self.sampling_window.set_status("")  # 清空错误信息
                    # 检查是否有有效的采样点数据
                    has_valid_points = (self.sampling_model.sampled_coords is not None and 
                                       len(self.sampling_model.sampled_coords) > 0)
                    self.sampling_window.enable_confirm(has_valid_points)
        except Exception as e:
            logger.error(f"排序模式变更处理失败: {str(e)}")
            self.sampling_window.set_status(f"排序模式变更处理失败: {str(e)}", True)
            self.sampling_window.enable_confirm(False)  # 禁用确认按钮

    def handle_tp_ovl_mode(self):
        """处理TP&OVL模式的特殊设置"""
        try:
            # 记录变更前的参数状态
            old_params = self.get_current_params()
            
            with self.sampling_window.batch_update():  # 使用现有的批量更新阻断信号
                self.sampling_window.set_sampling_method(SamplingMethod.EDGE.value)
                self.sampling_window.set_sampling_prefer(None)
                # 设置分离XY并设置采样尺寸
                self.sampling_window.separate_xy_checkbox.setChecked(True)
                self.sampling_window.sampling_size_x_input.setText("3")
                self.sampling_window.sampling_size_y_input.setText("3")
                
                # 显式调用UI更新方法
                self.sampling_window.update_input_visibility(True)  # 更新输入框显隐状态
                self.sampling_window.update_calc_label()  # 更新计算公式标签
            
            # 检查参数是否发生实际变化
            new_params = self.get_current_params()
            params_changed = (old_params != new_params)
            
            if params_changed:
                self.sampling_window.sampling_params_changed.emit()
            
        except Exception as e:
            logger.error(f"设置TP&OVL模式参数失败: {str(e)}")
            self.sampling_window.set_status("设置TP&OVL模式参数失败", True)
