from PySide6.QtCore import QObject, QStandardPaths, Qt, QThread, Signal
from PySide6.QtWidgets import QDialog
import pandas as pd
import importlib

from controllers.sampling_controller import Sampling<PERSON>ontroller
from models.results_model import ResultsModel
from utils.excel_handler import save_to_excel
from utils.parse_clipboard_data import parse_clipboard_data
from views.custom_widgets import CustomFileDialog
from views.main_window import MainWindow
from utils.derivative_strategies.derivative_factory import DerivativeStrategyFactory
from utils.main_clipboard_listener import MainClipboardListener

from contextlib import contextmanager


class ClipboardProcessor(QThread):
    # 定义一个信号，用于传递处理结果
    processed = Signal(object, object, object, object, object)

    def __init__(self, text, parent=None):
        super().__init__(parent)
        self.text = text

    def run(self):
        try:
            df, all_columns, x_col_name, y_col_name, shot_id_col_name = parse_clipboard_data(self.text)
            self.processed.emit(df, all_columns, x_col_name, y_col_name, shot_id_col_name)
        except Exception as e:
            self.processed.emit(None, None, None, None, None)


class FileSaver(QThread):
    # 定义一个信号，用于传递保存结果
    saved = Signal(bool, str)

    def __init__(self, filename, results_model, parent=None):
        super().__init__(parent)
        self.filename = filename
        self.results_model = results_model

    def run(self):
        # 调用 save_to_excel 并传递其返回值
        success, message = save_to_excel(self.filename, self.results_model)
        self.saved.emit(success, message)


class DerivativeCalculator(QThread):
    # 定义信号，用于传递计算结果
    calculated = Signal(object, int, object)  # 策略对象, 父索引, 计算结果

    def __init__(self, strategy, parent_index, parent_result, parent=None):
        super().__init__(parent)
        self.strategy = strategy
        self.parent_index = parent_index
        self.parent_result = parent_result

    def run(self):
        try:
            # 执行派生计算
            result = self.strategy.calculate(self.parent_result)
            
            # 发送计算成功信号
            self.calculated.emit(self.strategy, self.parent_index, result)
        except Exception as e:
            # 发送计算失败信号
            self.calculated.emit(self.strategy, self.parent_index, None)


class MainController(QObject):
    def __init__(self):
        super().__init__()
        self.is_sampling = False
        self.main_window = MainWindow()
        self.results_model = ResultsModel()
        self.main_window.set_results_model(self.results_model)
        self.setup_connections()

        self.sampling_controller = None
        self.last_clipboard_content = ""

        # 初始化剪贴板监听器
        self.clipboard_listener = MainClipboardListener(self)
        self.clipboard_listener.start_listening()

        # 连接窗口关闭信号
        self.main_window.aboutToClose.connect(self.cleanup)

    @contextmanager
    def clipboard_disconnect(self):
        # 暂停剪贴板监听
        self.clipboard_listener.stop_listening()
        try:
            yield
        finally:
            self.clipboard_listener.start_listening()

    def setup_connections(self):
        """设置信号与槽的连接"""
        self.main_window.save_all_signal.connect(self.save_all_results)
        self.main_window.cache_signal.connect(self.cache_results)
        self.main_window.load_progress_signal.connect(self.load_progress_file)
        self.main_window.edit_names_signal.connect(self.edit_names)
        self.main_window.edit_sampling_signal.connect(self.edit_sampling)
        self.main_window.delete_result_signal.connect(self.delete_result)
        self.main_window.create_dependency_signal.connect(self.show_dependency_dialog)
        self.main_window.edit_dependency_signal.connect(self.show_edit_dependency_dialog)
        
        # 连接Sheet相关的信号
        self.main_window.rename_sheet_signal.connect(self.rename_sheet)
        self.main_window.reorder_sheets_signal.connect(self.reorder_sheets)

    def handle_clipboard_data(self, df, all_columns, x_col_name, y_col_name, shot_id_col_name):
        """处理剪贴板数据的结果"""
        if df is not None and df.shape[0] > 0 and x_col_name and y_col_name:
            self.open_sampling_window(
                df, 
                shot_id_col_name=shot_id_col_name, 
                x_col_name=x_col_name, 
                y_col_name=y_col_name,
                all_columns=all_columns
            )
            self.main_window.set_status("坐标读取成功，打开采样窗口...")
        else:
            self.main_window.set_status("剪贴板中未识别到坐标数据", True)

    def set_status(self, message, is_error=False):
        """设置状态消息"""
        self.main_window.set_status(message, is_error)

    def show_dependency_dialog(self, strategy_type, parent_index):
        """显示创建派生点集的对话框
        
        Args:
            strategy_type: 策略类型
            parent_index: 父点集索引
        """
        if self.is_sampling:
            self.main_window.set_status("采样进行中，无法创建派生点集", True)
            return
        
        # 检查父点集是否有效
        if parent_index < 0 or parent_index >= self.results_model.rowCount():
            self.main_window.set_status("选择的父点集无效", True)
            return
        
        # 检查父点集是否为派生点集
        if self.results_model.is_dependent_result(parent_index):
            self.main_window.set_status("派生点集不能作为派生点集的源", True)
            return

        try:
            # 获取对话框类名
            dialog_class_name = DerivativeStrategyFactory.get_dialog_class(strategy_type)
            if not dialog_class_name:
                self.main_window.set_status(f"未找到策略 {strategy_type} 的对话框", True)
                return

            # 动态导入对话框类
            module_name = ''.join(['_' + c.lower() if c.isupper() else c for c in dialog_class_name]).lstrip('_')
            module = importlib.import_module(f"views.derivative_dialogs.{module_name}")
            dialog_class = getattr(module, dialog_class_name)

            # 在显示对话框前暂停剪贴板监听
            self.clipboard_listener.stop_listening()
            try:
                # 创建对话框实例
                dialog = dialog_class(self.main_window)

                # 显示对话框
                if dialog.exec() == QDialog.Accepted:
                    # 获取参数
                    params = dialog.get_params()
                    # 创建派生点集
                    self.create_dependency(strategy_type, parent_index, params)
                else:
                    # 用户取消了对话框，恢复剪贴板监听
                    self.clipboard_listener.start_listening()
            except Exception as e:
                # 确保发生异常时恢复剪贴板监听
                self.clipboard_listener.start_listening()
                self.main_window.set_status(f"显示派生点集对话框时出错: {str(e)}", True)
        except Exception as e:
            self.main_window.set_status(f"显示派生点集对话框时出错: {str(e)}", True)

    def show_edit_dependency_dialog(self, index, strategy_type):
        """显示编辑派生点集的对话框
        
        Args:
            index: 点集索引
            strategy_type: 策略类型
        """
        try:
            # 获取对话框类名
            dialog_class_name = DerivativeStrategyFactory.get_dialog_class(strategy_type)
            if not dialog_class_name:
                self.main_window.set_status(f"未找到策略 {strategy_type} 的对话框", True)
                return

            # 动态导入对话框类
            module_name = ''.join(['_' + c.lower() if c.isupper() else c for c in dialog_class_name]).lstrip('_')
            module = importlib.import_module(f"views.derivative_dialogs.{module_name}")
            dialog_class = getattr(module, dialog_class_name)

            # 获取当前参数
            params = self.results_model.get_dependency_params(index)
            if 'type' in params:
                del params['type']

            # 在显示对话框前暂停剪贴板监听
            with self.clipboard_disconnect():
                # 创建对话框实例
                dialog = dialog_class(self.main_window, **params)

                # 显示对话框
                if dialog.exec() == QDialog.Accepted:
                    # 获取参数
                    updated_params = dialog.get_params()
                    # 更新派生点集
                    self.edit_dependency(index, updated_params)
        except Exception as e:
            self.main_window.set_status(f"显示编辑派生点集对话框时出错: {str(e)}", True)

    def create_dependency(self, strategy_type, parent_index, params):
        """创建派生点集的通用方法，先计算后命名
        
        Args:
            strategy_type: 策略类型标识符
            parent_index: 父点集索引
            params: 策略参数字典
        """
        try:
            # 创建策略
            strategy = DerivativeStrategyFactory.create_strategy(strategy_type, **params)
            
            # 获取父点集结果
            parent_result = self.results_model.get_result(parent_index)
            if not parent_result:
                self.main_window.set_status("获取父点集数据失败", True)
                # 恢复剪贴板监听
                self.clipboard_listener.start_listening()
                return
                
            # 显示计算中状态
            self.main_window.set_status(f"正在计算{strategy.get_description()}...")
            
            # 创建并启动计算线程
            self.derivative_calculator = DerivativeCalculator(
                strategy, parent_index, parent_result, parent=self
            )
            self.derivative_calculator.calculated.connect(self.handle_derivative_calculation)
            self.derivative_calculator.start()
            
        except Exception as e:
            # 确保发生异常时恢复剪贴板监听
            self.clipboard_listener.start_listening()
            self.main_window.set_status(f"创建派生点集时出错: {str(e)}", True)

    def handle_derivative_calculation(self, strategy, parent_index, result):
        """处理派生计算结果
        
        Args:
            strategy: 策略对象
            parent_index: 父点集索引
            result: 计算结果
        """
        try:
            if result is None:
                self.main_window.set_status(f"计算{strategy.get_description()}失败", True)
                # 确保计算失败时恢复剪贴板监听
                self.clipboard_listener.start_listening()
                return
                
            # 计算成功，获取父点集名称作为默认值
            parent_sheet_name = self.results_model.get_sheet_name(parent_index)
            parent_project_name = self.results_model.get_project_name(parent_index)
            
            # 获取派生点集的名称，不预设sheet_name以允许用户选择不同的sheet
            sheet_name, project_name = self.main_window.get_names_dialog(
                sheet_name="",
                project_name=""
            )
            
            # 添加派生点集
            new_index = self.results_model.add_dependent_result(parent_index, strategy, sheet_name)
            
            if new_index >= 0:
                # 设置派生点集名称
                self.results_model.set_names(new_index, sheet_name, project_name)
                
                # 检查是否创建了新的sheet
                is_new_sheet = sheet_name not in self.results_model.get_unique_sheet_names()[:-1]
                
                # 更新UI以显示新的sheet和结果
                self.main_window.update_after_model_change(select_sheet=sheet_name)
                
                # 更新状态
                self.main_window.set_status(f"已创建{strategy.get_description()}")
            else:
                self.main_window.set_status("创建派生点集失败", True)
        finally:
            # 确保在所有情况下都恢复剪贴板监听
            self.clipboard_listener.start_listening()

    def edit_dependency(self, index, updated_params):
        """编辑派生点集的通用方法
        
        Args:
            index: 点集索引
            updated_params: 更新的参数字典
        """
        try:
            # 更新派生参数
            self.results_model.update_dependency_params(index, updated_params)
            
            # 获取更新后的描述
            description = self.results_model.get_dependency_description(index)
            
            # 更新状态
            self.main_window.set_status(f"已更新为{description}")
        except Exception as e:
            self.main_window.set_status(f"更新派生点集参数时出错: {str(e)}", True)

    def open_sampling_window(self, data, sampled_df=None, shot_id_col_name=None,
                             x_col_name=None, y_col_name=None, edit_index=None, 
                             previous_name=None, all_columns=None):
        """打开采样窗口"""
        previous_params = self.results_model.get_sampling_params(edit_index) if edit_index is not None else None
        
        # 保存原始数据的所有列信息
        self.original_data = data.copy()
        self.all_columns = all_columns
        self.x_col_name = x_col_name
        self.y_col_name = y_col_name
        
        # 停止剪贴板监听
        self.clipboard_listener.stop_listening()
        
        self.sampling_controller = SamplingController(
            data,
            self.handle_sampling_result,
            edit_index,
            previous_params,
            sampled_df,
            shot_id_col_name,
            x_col_name,
            y_col_name,
            previous_name,
        )
        self.main_window.aboutToClose.connect(self.sampling_controller.close_window)
        self.sampling_controller.show_window()
        self.is_sampling = True
        self.main_window.set_status("采样中，暂停监听")

    def handle_sampling_result(self, sampling_result):
        """处理采样结果"""
        self.is_sampling = False
        self.sampling_controller = None

        if sampling_result is not None:
            self.update_results(sampling_result)
            self.main_window.set_status(f"已添加/更新采样结果 {sampling_result['id'] + 1}")
        else:
            self.main_window.set_status("采样被中断，正在监听剪贴板...")

        # 恢复剪贴板监听
        self.clipboard_listener.start_listening()

    def update_results(self, sampling_result):
        """更新或添加采样结果"""
        current_params = sampling_result.get('params', {})
        
        # 如果有采样结果，将其与原始数据合并以保留所有列
        if 'sampled_df' in sampling_result and hasattr(self, 'original_data'):
            # 获取采样后的坐标数据
            sampled_df = sampling_result['sampled_df']

            # 确保原始数据和采样数据都有X和Y列
            if self.x_col_name and self.y_col_name and self.x_col_name in sampled_df.columns and self.y_col_name in sampled_df.columns:
                try:
                    # 创建一个只包含X和Y列的DataFrame用于匹配
                    sampled_coords = sampled_df[[self.x_col_name, self.y_col_name]].copy()
                    
                    # 将X和Y列转换为相同的数据类型，避免精度问题
                    sampled_coords[self.x_col_name] = pd.to_numeric(sampled_coords[self.x_col_name], errors='coerce')
                    sampled_coords[self.y_col_name] = pd.to_numeric(sampled_coords[self.y_col_name], errors='coerce')
                    
                    # 处理原始数据，确保X和Y列为数值类型
                    original_df_copy = self.original_data.copy()
                    original_df_copy[self.x_col_name] = pd.to_numeric(original_df_copy[self.x_col_name], errors='coerce')
                    original_df_copy[self.y_col_name] = pd.to_numeric(original_df_copy[self.y_col_name], errors='coerce')
                    
                    # 将采样坐标与原始数据合并，保留所有列
                    merged_df = pd.merge(
                        sampled_coords,
                        original_df_copy,
                        on=[self.x_col_name, self.y_col_name],
                        how='left'
                    )

                    # 更新采样结果中的DataFrame
                    sampling_result['sampled_df'] = merged_df
                    
                    # 保存所有列信息
                    sampling_result['all_columns'] = self.all_columns
                    
                    # 保存X和Y列名
                    sampling_result['x_col_name'] = self.x_col_name
                    sampling_result['y_col_name'] = self.y_col_name
                except Exception as e:
                    self.main_window.set_status(str(e), True)
            else:
                self.main_window.set_status("X或Y列缺失，无法合并数据。", True)
        else:
            self.main_window.set_status("缺少采样结果或原始数据，无法合并", True)

        # 更新或添加结果
        target_sheet = None
        if sampling_result['id'] != -1 and sampling_result['id'] < self.results_model.rowCount():
            # 如果是更新现有结果，保留原有的sheet_name
            sheet_name = self.results_model.get_sheet_name(sampling_result['id'])
            self.results_model.update_result(sampling_result['id'], sampling_result, current_params)
            target_sheet = sheet_name
            # 更新UI
            self.main_window.update_after_model_change(select_sheet=target_sheet)
        else:
            # 如果是新结果，需要选择sheet_name和project_name
            with self.clipboard_disconnect():
                sheet_name, project_name = self.main_window.get_names_dialog()
                new_id = self.results_model.add_result(sampling_result, current_params, sheet_name, project_name)
                target_sheet = sheet_name
                # 更新UI
                self.main_window.update_after_model_change(select_sheet=target_sheet)

    def edit_names(self, index):
        """编辑结果的项目名称（不再修改sheet名）"""
        if self.is_sampling:
            self.main_window.set_status("采样进行中，无法重命名", True)
            return

        with self.clipboard_disconnect():
            if index >= 0:
                current_sheet_name = self.results_model.get_sheet_name(index)
                current_project_name = self.results_model.get_project_name(index)
                # 只修改project_name
                project_name = self.main_window.get_project_name_dialog(current_project_name)
                if project_name != current_project_name:
                    self.results_model.set_names(index, current_sheet_name, project_name)
                    # 更新UI，保持在当前sheet
                    self.main_window.update_after_model_change(select_sheet=current_sheet_name)

    def rename_sheet(self, old_name, new_name):
        """重命名Sheet"""
        if self.is_sampling:
            self.main_window.set_status("采样进行中，无法重命名标签页", True)
            return
            
        # 检查新名称是否有效
        if not new_name or old_name == new_name:
            return
            
        # 重命名Sheet
        if self.results_model.rename_sheet(old_name, new_name):
            # 更新UI
            self.main_window.handle_sheet_rename(old_name, new_name)
            self.main_window.set_status(f"已将标签页 '{old_name}' 重命名为 '{new_name}'")
        else:
            self.main_window.set_status(f"重命名标签页失败，可能存在重名", True)

    def reorder_sheets(self, new_order):
        """重新排序Sheet"""
        if self.is_sampling:
            self.main_window.set_status("采样进行中，无法重排标签页", True)
            return
            
        # 重排序Sheet
        if self.results_model.reorder_sheets(new_order):
            self.main_window.set_status("标签页顺序已更新")
        else:
            self.main_window.set_status("更新标签页顺序失败", True)

    def edit_sampling(self, index):
        """编辑采样结果"""
        if self.is_sampling:
            self.main_window.set_status("采样进行中，无法编辑采样结果", True)
            return
            
        # 检查是否为派生点集
        if self.results_model.is_dependent_result(index):
            # 获取父点集信息
            parent_id = self.results_model.results[index].get('parent_id')
            if parent_id is not None and 0 <= parent_id < self.results_model.rowCount():
                parent = self.results_model.results[parent_id]
                parent_name = parent['project_name']
                parent_sheet = parent['sheet_name']
                # 构建更具体的错误消息，包含父点集信息
                self.main_window.set_status(f"派生点集不能直接编辑，请编辑其源点集: {parent_sheet}-{parent_name}", True)
            else:
                self.main_window.set_status("派生点集不能直接编辑，请编辑其源点集", True)
            return

        result = self.results_model.get_result(index)
        sheet_name = self.results_model.get_sheet_name(index)
        project_name = self.results_model.get_project_name(index)
        if result:
            # 保存原始数据的所有列信息，用于后续合并
            self.original_data = result.get('original_data')
            self.all_columns = result.get('all_columns', [])
            self.x_col_name = result.get('x_col_name')
            self.y_col_name = result.get('y_col_name')
            
            self.open_sampling_window(
                data=result.get('original_data'),
                sampled_df=result.get('sampled_df'),
                shot_id_col_name=result.get('shot_id_col_name'),
                x_col_name=result.get('x_col_name'),
                y_col_name=result.get('y_col_name'),
                edit_index=index,
                previous_name=sheet_name + '-' + project_name,
                all_columns=self.all_columns
            )

    def delete_result(self, index):
        """删除指定索引的结果"""
        if self.is_sampling:
            self.main_window.set_status("采样进行中，无法删除采样结果", True)
            return

        if 0 <= index < self.results_model.rowCount():
            # 获取当前sheet名称以便删除后保持在当前sheet
            current_sheet_name = self.results_model.get_sheet_name(index)
            
            # 获取当前行的显示内容
            result_name = self.results_model.data(self.results_model.index(index), Qt.DisplayRole)
            # 执行删除操作
            self.results_model.removeRows(index, 1)
            # 设置状态消息
            message = "{current_sheet_name}-{result_name} 已删除"
            self.main_window.set_status(message)
            # 更新UI
            self.main_window.update_after_model_change(select_sheet=current_sheet_name)

    def save_all_results(self):
        """保存所有结果到文件"""
        if self.is_sampling:
            self.main_window.set_status("采样进行中，无法保存", True)
            return

        if not self.results_model.get_all_results():
            self.main_window.set_status("采样结果为空")
            return

        with self.clipboard_disconnect():
            try:
                desktop_path = QStandardPaths.writableLocation(QStandardPaths.DesktopLocation)
            except Exception:
                desktop_path = ""

            filename, _ = CustomFileDialog.getSaveFileName(
                self.main_window,
                caption="保存采样结果",
                dir=desktop_path,
                filter="Excel 文件 (*.xlsx)",
            )

            if not filename:  # 用户取消保存
                self.main_window.set_status("保存被取消")
                return

            # 创建并启动后台线程来保存文件
            self.file_saver = FileSaver(filename, self.results_model, parent=self)
            self.file_saver.saved.connect(self.handle_save_result)
            self.file_saver.start()

    def cache_results(self):
        """暂存当前进度"""
        if self.is_sampling:
            self.main_window.set_status("采样进行中，无法暂存", True)
            return

        if not self.results_model.get_all_results():
            self.main_window.set_status("采样结果为空")
            return

        with self.clipboard_disconnect():
            try:
                desktop_path = QStandardPaths.writableLocation(QStandardPaths.DesktopLocation)
            except Exception:
                desktop_path = ""

            filename, _ = CustomFileDialog.getSaveFileName(
                self.main_window,
                caption="暂存进度",
                dir=desktop_path,
                filter="工程文件 (*.pkl)",
            )

            if not filename:  # 用户取消保存
                self.main_window.set_status("暂存被取消")
                return

            self.results_model.save_progress_to_file(filename)
            self.main_window.set_status(f"已暂存到 {filename}")

    def load_progress_file(self):
        """读取进度文件"""
        if self.is_sampling:
            self.main_window.set_status("采样进行中，无法读取进度文件", True)
            return

        # 暂停剪贴板监听
        with self.clipboard_disconnect():
            try:
                desktop_path = QStandardPaths.writableLocation(QStandardPaths.DesktopLocation)
            except Exception:
                desktop_path = ""

            filename, _ = CustomFileDialog.getOpenFileName(
                self.main_window,
                caption="读取进度",
                dir=desktop_path,
                filter="工程文件 (*.pkl)",
            )

            if not filename:  # 用户取消读取
                self.main_window.set_status("读取进度被取消")
                return

            success, message = self.results_model.load_project(filename)
            if success:
                # 获取第一个sheet（如果有）
                sheet_names = self.results_model.get_ordered_sheet_names()
                first_sheet = sheet_names[0] if sheet_names else None
                
                # 更新UI，选择第一个sheet
                self.main_window.update_after_model_change(select_sheet=first_sheet)
                self.main_window.set_status(f"已载入进度: {filename}")
            else:
                self.main_window.set_status(message, True)

    def handle_save_result(self, success, message):
        """处理文件保存结果"""
        if success:
            self.main_window.set_status(f"已保存到 {message}")
        else:
            self.main_window.set_status(message, True)

    def show_main_window(self):
        """显示主窗口"""
        self.main_window.show()
        self.main_window.windowHandle().requestActivate()

        # 检查是否存在临时文件
        if self.results_model.has_auto_saved_file():
            with self.clipboard_disconnect():

                if not self.main_window.show_restore_dialog():
                    # 用户选择不恢复，删除临时文件
                    self.results_model.cleanup_temp_file()
                    # 重新初始化结果模型
                    self.results_model = ResultsModel()
                    self.main_window.set_results_model(self.results_model)
                    return

                success, message = self.results_model.load_auto_save()
                if success:
                    # 获取第一个sheet（如果有）
                    sheet_names = self.results_model.get_ordered_sheet_names()
                    first_sheet = sheet_names[0] if sheet_names else None
                    
                    # 更新UI，选择第一个sheet
                    self.main_window.update_after_model_change(select_sheet=first_sheet)
                    self.main_window.set_status("已恢复上次的结果")
                else:
                    self.main_window.set_status(f"恢复失败: {message}", True)

    def cleanup(self):
        """程序退出时的清理工作"""
        if self.results_model:
            self.results_model.cleanup_temp_file()
        if self.clipboard_listener:
            self.clipboard_listener.cleanup()
